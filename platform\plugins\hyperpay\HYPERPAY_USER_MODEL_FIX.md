# HyperPay User Model Fix

## Issue Description

The error occurred because the `HyperPayApiService::checkout()` method expected either `null` or an actual `Illuminate\Database\Eloquent\Model` instance, but was receiving an anonymous class object.

**Error Message:**
```
Bo<PERSON>ble\HyperPay\Services\HyperPayApiService::checkout(): Argument #2 ($user) must be of type ?Illuminate\Database\Eloquent\Model, class@anonymous given
```

## Root Cause

The issue was in the `createCheckout` method in `HyperPayPaymentAbstract.php` where I was creating an anonymous class to simulate a user object:

```php
// PROBLEMATIC CODE (FIXED)
$user = new class {
    public $email;
    public $name;
    // ...
};
```

This anonymous class doesn't extend `Illuminate\Database\Eloquent\Model`, so it failed the type check.

## Solution Implemented

### 1. **Updated createCheckout Method**
- Removed the anonymous class creation
- Now attempts to load the actual customer model if `customer_id` and `customer_type` are provided
- Falls back to `null` if the model cannot be loaded
- Extracts customer information from multiple sources (direct data, user model, address data)

### 2. **Enhanced HyperPayApiService**
- Modified `prepareCheckout` to handle `null` user objects
- Creates a simple object with customer data when user is `null` but customer info is available
- Passes this to the `HttpParameters` class for processing

### 3. **Updated HttpParameters Class**
- Changed type hints to accept `Model|object|null` instead of just `Model`
- Can now handle both Eloquent models and simple objects with customer data

## Code Changes

### HyperPayPaymentAbstract.php
```php
// Extract customer information from various sources
$customerEmail = $data['customer_email'] ?? 
                ($user ? $user->email : null) ?? 
                (isset($data['address']['email']) ? $data['address']['email'] : null);

$customerName = $data['customer_name'] ?? 
               ($user ? $user->name : null) ?? 
               (isset($data['address']['name']) ? $data['address']['name'] : null);

// If we have customer_id and customer_type, try to get the actual user model
if (!empty($data['customer_id']) && !empty($data['customer_type'])) {
    try {
        $customerType = $data['customer_type'];
        if (class_exists($customerType)) {
            $user = $customerType::find($data['customer_id']);
        }
    } catch (\Exception $e) {
        Log::warning('Could not load customer model', [
            'customer_id' => $data['customer_id'],
            'customer_type' => $data['customer_type'],
            'error' => $e->getMessage()
        ]);
    }
}
```

### HyperPayApiService.php
```php
// If user is null, create a simple object with customer data from trackableData
$customerData = $user;
if (!$user && (!empty($trackableData['customer_email']) || !empty($trackableData['customer_name']))) {
    $customerData = (object) [
        'email' => $trackableData['customer_email'] ?? null,
        'name' => $trackableData['customer_name'] ?? null,
    ];
}

$parameters = $httpParameters->postParams(
    Arr::get($trackableData, 'amount'),
    $customerData, // Pass the customer data object or user model
    $this->config,
    $this->billing,
    $this->registerUserCard
);
```

### HttpParameters.php
```php
/**
 * @param  Model|object|null  $user
 */
public function postParams($amount, $user, $hyperPayConfig, $billing = null, $registerUser = false): array

/**
 * @param  Model|object|null  $user
 */
protected function getBodyParameters($amount, $user, $hyperPayConfig): array
```

## Testing

### New Test Route Added
- `/payment/hyperpay/debug/test-user-model-fix` - Tests the fix with the same data structure that caused the error

### Test Method
```php
public function testUserModelFix(Request $request)
{
    // Simulates the exact data structure from the error logs
    $testData = [
        'amount' => 528.0,
        'currency' => 'SAR',
        'order_id' => [53], // Array format like in the error
        'payment_type' => 'visa',
        'customer_id' => 2,
        'customer_type' => 'Botble\\Ecommerce\\Models\\Customer',
        'address' => [
            'name' => 'Test Customer',
            'email' => '<EMAIL>',
            // ... other address fields
        ]
    ];
    
    $result = $this->hyperPayService->createCheckout($testData);
    // Returns success/error response
}
```

## Benefits of the Fix

1. **Type Safety**: Properly handles the type requirements of the API service
2. **Flexibility**: Can work with actual user models or extract customer data from other sources
3. **Backward Compatibility**: Existing code continues to work
4. **Error Handling**: Graceful fallback when user models cannot be loaded
5. **Data Extraction**: Intelligently extracts customer information from multiple data sources

## Data Source Priority

The fix extracts customer information in this priority order:

1. **Direct customer data** (`customer_email`, `customer_name`)
2. **User model data** (if successfully loaded)
3. **Address data** (`address.email`, `address.name`)

## Error Handling

- If customer model loading fails, it logs a warning and continues with `null`
- If no customer data is available, it passes `null` to the API service
- The API service creates a simple object with available customer data when needed

## Next Steps

1. Test the fix using the new debug route: `/payment/hyperpay/debug/test-user-model-fix`
2. Monitor logs to ensure the error no longer occurs
3. Verify that checkout creation works properly with the fix
4. Consider adding more robust customer data validation if needed

The fix maintains the structured approach of the new API service while properly handling the type requirements and data extraction needs of the HyperPay integration.
