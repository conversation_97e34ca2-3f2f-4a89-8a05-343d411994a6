<?php

namespace Bo<PERSON>ble\HyperPay\Services\Support;

use Bo<PERSON>ble\HyperPay\Contracts\BillingInterface;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;

class HttpParameters
{
    /**
     * Get the parameters that used in the request with HyperPay
     * to initialize the transaction and generate the form.
     *
     * @param  float  $amount
     * @param  Model|object|null  $user
     * @param  array  $hyperPayConfig
     * @param  \Botble\HyperPay\Contracts\BillingInterface|null  $billing
     * @param  bool  $registerUser
     * @return array
     */
    public function postParams($amount, $user, $hyperPayConfig, $billing = null, $registerUser = false): array
    {
        $body = $this->getBodyParameters($amount, $user, $hyperPayConfig);

        if ($registerUser) {
            $body = array_merge($body, $this->registerPaymentData());
        }

        $billing_parameters = $this->getBillingParameters($billing);

        $parameters = array_merge($body, $billing_parameters);

        Log::info('HyperPay HTTP Parameters Generated', [
            'amount' => $amount,
            'currency' => $hyperPayConfig['currency'] ?? 'SAR',
            'entity_id' => $hyperPayConfig['entityId'] ?? 'not_set',
            'merchant_transaction_id' => $hyperPayConfig['merchantTransactionId'] ?? 'not_set',
            'register_user' => $registerUser,
            'has_billing' => !empty($billing_parameters),
            'parameter_count' => count($parameters)
        ]);

        return $parameters;
    }

    /**
     * Get the entity id based on the checkout id if it's for VISA/MASTER or MADA.
     *
     * @param  string  $checkoutId
     * @return array
     */
    public function getParams($checkoutId): array
    {
        $entityId = $this->getEntityId($checkoutId);

        return ['entityId' => $entityId];
    }

    /**
     * Generate the params that used in the recurring payment.
     *
     * @param  string  $amount
     * @param  string  $shopperResultUrl
     * @param  string  $checkoutId  That define the entity_id related to the registration id.
     * @return array
     */
    public function postRecurringPayment($amount, $shopperResultUrl, $checkoutId)
    {
        $currency = get_payment_setting('currency', HYPERPAY_PAYMENT_METHOD_NAME, 'SAR');

        return array_merge([
            'standingInstruction.mode' => 'REPEATED',
            'standingInstruction.type' => 'RECURRING',
            'standingInstruction.source' => 'MIT',
            'amount' => $amount,
            'currency' => $currency,
            'paymentType' => 'PA',
            'shopperResultUrl' => $shopperResultUrl,
        ], $this->getParams($checkoutId));
    }

    /**
     * Generate the basic user parameters.
     *
     * @param  float  $amount
     * @param  Model|object|null  $user
     * @param  array  $hyperPayConfig
     * @return array
     */
    protected function getBodyParameters($amount, $user, $hyperPayConfig): array
    {
        $body_parameters = [
            'entityId' => $hyperPayConfig['entityId'],
            'amount' => number_format($amount, 2, '.', ''),
            'currency' => $hyperPayConfig['currency'],
            'paymentType' => 'DB',
            'merchantTransactionId' => $hyperPayConfig['merchantTransactionId'],
        ];

        // Add notification URL if configured
        if (!empty($hyperPayConfig['notificationUrl'])) {
            $body_parameters['notificationUrl'] = url('/') . $hyperPayConfig['notificationUrl'];
        }

        // Add customer information if user is provided
        if ($user) {
            if (!empty($user->email)) {
                $body_parameters['customer.email'] = $user->email;
            }

            if (!empty($user->name)) {
                $nameParts = explode(' ', trim($user->name), 2);
                $body_parameters['customer.givenName'] = $nameParts[0];
                $body_parameters['customer.surname'] = isset($nameParts[1]) ? $nameParts[1] : $nameParts[0];
            }
        }

        return $body_parameters;
    }

    /**
     * The init recurring payment params.
     *
     * @return array
     */
    protected function registerPaymentData()
    {
        return [
            'standingInstruction.mode' => 'INITIAL',
            'standingInstruction.type' => 'RECURRING',
            'standingInstruction.source' => 'CIT',
            'createRegistration' => true,
        ];
    }

    /**
     * Get the billing data from the Billing class if a user generates one.
     *
     * @param  \Botble\HyperPay\Contracts\BillingInterface|null  $billing
     * @return array
     */
    protected function getBillingParameters($billing): array
    {
        if ($billing instanceof BillingInterface) {
            return $billing->getBillingData();
        }

        return [];
    }

    /**
     * Find the entityId from the transaction if it's for MADA or else.
     *
     * @param  string  $id  transaction id (currently unused, using session data instead)
     * @return string
     */
    protected function getEntityId($id)
    {
        // Try to get transaction from session first
        // Note: $id parameter is currently unused as we rely on session data
        $paymentType = session('hyperpay_payment_type', 'visa');

        $entityId = get_payment_setting('visa_entity_id', HYPERPAY_PAYMENT_METHOD_NAME);

        if ($paymentType === 'mada') {
            $entityId = get_payment_setting('mada_entity_id', HYPERPAY_PAYMENT_METHOD_NAME);
        } elseif ($paymentType === 'applepay') {
            $entityId = get_payment_setting('applepay_entity_id', HYPERPAY_PAYMENT_METHOD_NAME);
        } elseif ($paymentType === 'amex') {
            $entityId = get_payment_setting('amex_entity_id', HYPERPAY_PAYMENT_METHOD_NAME);
        }

        return $entityId;
    }

    /**
     * Create parameters for checkout creation.
     *
     * @param  array  $data
     * @return array
     */
    public function createCheckoutParams(array $data): array
    {
        $amount = number_format((float) $data['amount'], 2, '.', '');
        $currency = strtoupper($data['currency'] ?? 'SAR');
        $paymentType = $data['payment_type'] ?? 'visa';

        // Get entity ID based on payment type
        $entityId = $this->getEntityIdByPaymentType($paymentType);

        $params = [
            'entityId' => $entityId,
            'amount' => $amount,
            'currency' => $currency,
            'paymentType' => 'DB',
            'merchantTransactionId' => $data['order_id'] ?? uniqid('order_'),
        ];

        // Add shopper result URL
        if (!empty($data['shopperResultUrl'])) {
            $params['shopperResultUrl'] = $data['shopperResultUrl'];
        }

        // Add customer information
        if (!empty($data['customer_email'])) {
            $params['customer.email'] = $data['customer_email'];
        }

        if (!empty($data['customer_name'])) {
            $nameParts = explode(' ', trim($data['customer_name']), 2);
            $params['customer.givenName'] = $nameParts[0];
            $params['customer.surname'] = isset($nameParts[1]) ? $nameParts[1] : $nameParts[0];
        }

        // Add billing information
        if (!empty($data['billing_address'])) {
            $params['billing.street1'] = substr($data['billing_address'], 0, 50);
        }

        if (!empty($data['billing_city'])) {
            $params['billing.city'] = substr($data['billing_city'], 0, 32);
            $params['billing.state'] = substr($data['billing_city'], 0, 32);
        }

        if (!empty($data['billing_country'])) {
            $country = strtoupper($data['billing_country']);
            if (strlen($country) === 2) {
                $params['billing.country'] = $country;
            }
        }

        if (!empty($data['billing_postcode'])) {
            $params['billing.postcode'] = substr($data['billing_postcode'], 0, 10);
        }

        return $params;
    }

    /**
     * Get entity ID by payment type.
     *
     * @param  string  $paymentType
     * @return string
     */
    protected function getEntityIdByPaymentType(string $paymentType): string
    {
        $entityIdKey = match($paymentType) {
            'visa', 'master' => 'visa_entity_id',
            'mada' => 'mada_entity_id',
            'amex' => 'amex_entity_id',
            'applepay' => 'applepay_entity_id',
            default => 'visa_entity_id'
        };

        return get_payment_setting($entityIdKey, HYPERPAY_PAYMENT_METHOD_NAME);
    }
}
