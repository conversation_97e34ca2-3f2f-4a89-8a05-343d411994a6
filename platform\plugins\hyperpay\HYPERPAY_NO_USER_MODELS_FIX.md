# HyperPay No User Models Fix

## Issue Description

After fixing the first user model type error, another similar error occurred in the `HttpResponse::setUser()` method:

```
<PERSON><PERSON><PERSON>\HyperPay\Services\Support\HttpResponse::setUser(): Argument #1 ($user) must be of type Illuminate\Database\Eloquent\Model, null given
```

## Root Cause Analysis

The issue was that the HyperPay implementation was trying to use Eloquent Models and objects for customer data, which is different from how other payment plugins in the project handle customer information.

## Solution: Follow Other Plugins Pattern

After examining other payment plugins like PayMob, PayPal, and Stripe, I found they all extract customer data directly from the `$data['address']` array without using any user models or objects.

### Example from PayMob Plugin:
```php
$name_parts = explode(" ", $data['address']['name']);
$first_name = $name_parts[0]??'';
$last_name = $name_parts[1]??'..';

"billing_data" => [
    "email" => $data['address']['email'],
    "first_name" => $first_name,
    "last_name" => $last_name,
    "street" => !empty($data['address']['address']) ? $data['address']['address'] : 'NA',
    "phone_number" => !empty($data['address']['phone']) ? $data['address']['phone'] : 'NA',
    "city" => !empty($data['address']['city']) ? $data['address']['city'] : 'NA',
    "country" => !empty($data['address']['country']) ? $data['address']['country'] : 'NA',
]
```

## Implementation Changes

### 1. **Updated HttpParameters Class**
- Changed from accepting `Model|object|null` to accepting `array` for customer data
- Removed dependency on Eloquent Models
- Extract customer info from array data like other plugins

```php
/**
 * @param  array  $customerData
 */
public function postParams($amount, $customerData, $hyperPayConfig, $billing = null, $registerUser = false): array

protected function getBodyParameters($amount, $customerData, $hyperPayConfig): array
{
    // Add customer information from customer data array (like other plugins)
    if (!empty($customerData['email'])) {
        $body_parameters['customer.email'] = $customerData['email'];
    }
    
    if (!empty($customerData['name'])) {
        $nameParts = explode(' ', trim($customerData['name']), 2);
        $body_parameters['customer.givenName'] = $nameParts[0];
        $body_parameters['customer.surname'] = isset($nameParts[1]) ? $nameParts[1] : $nameParts[0];
    }
}
```

### 2. **Updated HyperPayApiService**
- Simplified customer data handling to use arrays only
- No more object creation or model dependencies

```php
// Extract customer data as array (like other plugins)
$customerData = [
    'email' => $trackableData['customer_email'] ?? null,
    'name' => $trackableData['customer_name'] ?? null,
];

$parameters = $httpParameters->postParams(
    Arr::get($trackableData, 'amount'),
    $customerData, // Pass array instead of object/model
    $this->config,
    $this->billing,
    $this->registerUserCard
);
```

### 3. **Updated Abstract Payment Class**
- Extract customer data from address array like other plugins
- Always pass `null` for user model to avoid type errors

```php
// Extract customer information from address data (like PayMob and other plugins)
$customerEmail = $data['customer_email'] ?? 
                (isset($data['address']['email']) ? $data['address']['email'] : null);

$customerName = $data['customer_name'] ?? 
               (isset($data['address']['name']) ? $data['address']['name'] : null);

// Create checkout using the new API service (no user model needed)
$result = $apiService->checkout(
    $trackableData,
    null, // Don't pass user model - use customer data from trackableData
    $data['amount'],
    $data['payment_type'] ?? 'visa',
    request()
);
```

### 4. **Updated HttpResponse Class**
- Made user parameter nullable to handle `null` values properly

```php
/**
 * @param  Model|null  $user
 */
public function setUser(?Model $user): self
{
    $this->user = $user;
    return $this;
}
```

## Benefits of This Approach

1. **Consistency**: Follows the same pattern as other payment plugins in the project
2. **Simplicity**: No complex user model loading or object creation
3. **Reliability**: Eliminates type errors related to user models
4. **Maintainability**: Easier to understand and maintain
5. **Performance**: No database queries to load user models

## Data Flow

1. **Payment data comes in** with `address` array containing customer info
2. **Extract customer data** from `$data['address']['email']` and `$data['address']['name']`
3. **Pass as simple array** to HttpParameters class
4. **Build API parameters** using array data
5. **Send to HyperPay API** with proper customer information

## Testing

### Updated Test Route
- `/payment/hyperpay/debug/test-user-model-fix` - Tests the new approach without user models

### Test Data Structure
```php
$testData = [
    'amount' => 528.0,
    'currency' => 'SAR',
    'order_id' => [53],
    'payment_type' => 'visa',
    'customer_id' => 2,
    'customer_type' => 'Botble\\Ecommerce\\Models\\Customer',
    'address' => [
        'name' => 'Test Customer',
        'email' => '<EMAIL>',
        'phone' => '+1234567890',
        'country' => 'SA',
        // ... other address fields
    ]
];
```

## Files Modified

1. **HttpParameters.php** - Changed to accept array instead of models
2. **HyperPayApiService.php** - Simplified customer data handling
3. **HyperPayPaymentAbstract.php** - Extract data from address array
4. **HyperPayPaymentService.php** - Updated API service methods
5. **HttpResponse.php** - Made user parameter nullable
6. **HyperPayController.php** - Updated test method

## Key Differences from Previous Approach

| Previous Approach | New Approach |
|------------------|--------------|
| Used Eloquent Models | Uses simple arrays |
| Created anonymous classes | No object creation |
| Complex user model loading | Direct data extraction |
| Type errors with models | No type conflicts |
| Different from other plugins | Consistent with other plugins |

## Result

The HyperPay plugin now handles customer data exactly like other payment plugins in the project:
- Extracts customer information from the `address` array
- Uses simple array data structures
- No dependency on user models or objects
- Eliminates all type-related errors
- Maintains full functionality for payment processing

This approach is more robust, consistent, and follows the established patterns in the codebase.
