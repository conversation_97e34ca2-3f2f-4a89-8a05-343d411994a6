{"__meta": {"id": "01K2Q9H02HNJZGANAFPEJAZANJ", "datetime": "2025-08-15 16:38:06", "utime": **********.674217, "method": "GET", "uri": "/payment/hyperpay/callback?id=460F3D223F84416536B101539705D3A8.uat01-vm-tx02&resourcePath=%2Fv1%2Fcheckouts%2F460F3D223F84416536B101539705D3A8.uat01-vm-tx02%2Fpayment", "ip": "127.0.0.1"}, "messages": {"count": 7, "messages": [{"message": "[16:38:05] LOG.info: HyperPay Callback Received {\n    \"resource_path\": \"\\/v1\\/checkouts\\/460F3D223F84416536B101539705D3A8.uat01-vm-tx02\\/payment\",\n    \"checkout_id\": \"460F3D223F84416536B101539705D3A8.uat01-vm-tx02\",\n    \"all_params\": {\n        \"id\": \"460F3D223F84416536B101539705D3A8.uat01-vm-tx02\",\n        \"resourcePath\": \"\\/v1\\/checkouts\\/460F3D223F84416536B101539705D3A8.uat01-vm-tx02\\/payment\"\n    },\n    \"session_data\": {\n        \"hyperpay_checkout_id\": \"460F3D223F84416536B101539705D3A8.uat01-vm-tx02\",\n        \"hyperpay_order_id\": 53,\n        \"hyperpay_amount\": 528,\n        \"hyperpay_currency\": \"SAR\",\n        \"hyperpay_payment_type\": \"visa\"\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.021747, "xdebug_link": null, "collector": "log"}, {"message": "[16:38:05] LOG.info: HyperPay Payment Status Check {\n    \"url\": \"https:\\/\\/eu-test.oppwa.com\\/v1\\/checkouts\\/460F3D223F84416536B101539705D3A8.uat01-vm-tx02\\/payment\",\n    \"checkout_id\": \"460F3D223F84416536B101539705D3A8.uat01-vm-tx02\",\n    \"resource_path\": \"\\/v1\\/checkouts\\/460F3D223F84416536B101539705D3A8.uat01-vm-tx02\\/payment\",\n    \"payment_type\": \"visa\",\n    \"entity_id\": \"8ac7a4c79483092601948366b9d1011b\",\n    \"query_params\": {\n        \"entityId\": \"8ac7a4c79483092601948366b9d1011b\"\n    },\n    \"status_code\": 200,\n    \"response\": {\n        \"result\": {\n            \"code\": \"200.300.404\",\n            \"description\": \"invalid or missing parameter\",\n            \"parameterErrors\": [\n                {\n                    \"name\": \"shopperResultUrl\",\n                    \"value\": \"https:\\/\\/martfury.gc\\/payment\\/hyperpay\\/callback?checkout_id=460F3D223F84416536B101539705D3A8.uat01-vm-tx02&order_id=\",\n                    \"message\": \"was already set and cannot be overwritten\"\n                }\n            ]\n        },\n        \"buildNumber\": \"8aa3728a6ba24ed3c4a6c6e9064ffd726f1bee67@2025-08-11 05:22:09 +0000\",\n        \"timestamp\": \"2025-08-15 16:38:04+0000\",\n        \"ndc\": \"460F3D223F84416536B101539705D3A8.uat01-vm-tx02\"\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.76493, "xdebug_link": null, "collector": "log"}, {"message": "[16:38:05] LOG.info: HyperPay Payment Result Analysis {\n    \"result_code\": \"200.300.404\",\n    \"result_description\": \"invalid or missing parameter\",\n    \"checkout_id\": \"460F3D223F84416536B101539705D3A8.uat01-vm-tx02\",\n    \"charge_id\": \"460F3D223F84416536B101539705D3A8.uat01-vm-tx02\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.769133, "xdebug_link": null, "collector": "log"}, {"message": "[16:38:05] LOG.info: HyperPay Processing Payment Hook {\n    \"order_id\": 53,\n    \"order_ids\": [\n        53\n    ],\n    \"amount\": 528,\n    \"currency\": \"SAR\",\n    \"status\": \"failed\",\n    \"charge_id\": \"460F3D223F84416536B101539705D3A8.uat01-vm-tx02\",\n    \"customer_id\": 2,\n    \"customer_type\": \"Botble\\\\Ecommerce\\\\Models\\\\Customer\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.769749, "xdebug_link": null, "collector": "log"}, {"message": "[16:38:06] LOG.debug: From: Example <<EMAIL>>\r\nTo: kamron.<PERSON><PERSON><PERSON><PERSON>@example.org\r\nSubject: New order(s) at MartFury\r\nMIME-Version: 1.0\r\nDate: Fri, 15 Aug 2025 16:38:06 +0000\r\nMessage-ID: <<EMAIL>>\r\nContent-Type: text/html; charset=utf-8\r\nContent-Transfer-Encoding: quoted-printable\r\n\r\n<!doctype html>\r\n<html lang=\"en\">\r\n\r\n<head>\r\n    <meta charset=\"UTF-8\">\r\n    <title>MartFury</title>\r\n</head>\r\n\r\n<body class=\"bb-bg-body\" dir=\"ltr\" style=\"margin: 0; padding: 0; font-size: 14px; line-height: 171.4285714286%; mso-line-height-rule: exactly; color: #3A4859; width: 100%; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale; -webkit-font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11'; font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11'; font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; background-color: #f6f7f9;\">\r\n\r\n<center>\r\n    <table class=\"bb-main bb-bg-body\" width=\"100%\" cellspacing=\"0\" cellpadding=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; background-color: #f6f7f9;\">\r\n        <tbody>\r\n            <tr>\r\n                <td align=\"center\" valign=\"top\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;\">\r\n                    <table class=\"bb-wrap\" cellspacing=\"0\" cellpadding=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; max-width: 640px; text-align: left; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\r\n                        <tbody>\r\n                            <tr>\r\n                                <td class=\"bb-p-sm\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 8px;\">\r\n                                    <table cellpadding=\"0\" cellspacing=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\r\n                                        <tbody>\r\n                                            <tr>\r\n                                                <td class=\"bb-py-lg\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding-top: 24px; padding-bottom: 24px;\">\r\n                                                    <table cellspacing=\"0\" cellpadding=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\r\n                                                        <tbody>\r\n                                                            <tr>\r\n                                                                <td class=\"bb-text-left\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: left;\">\r\n                                                                    <a href=\"https://martfury.gc\" style=\"color: #206bc4; text-decoration: none;\">\r\n                                                                        <img class=\"bb-logo\" src=\"https://martfury.gc/storage/general/logo.png\" alt=\"MartFury\" style=\"line-height: 100%; outline: none; text-decoration: none; vertical-align: baseline; font-size: 0; border: 0 none; max-height: 40px;\">\r\n                                                                    </a>\r\n                                                                </td>\r\n                                                                <td class=\"bb-text-right\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right;\">\r\n                                                                    2025-08-15 16:38:06\r\n                                                                </td>\r\n                                                            </tr>\r\n                                                        </tbody>\r\n                                                    </table>\r\n                                                </td>\r\n                                            </tr>\r\n                                        </tbody>\r\n                                    </table>\r\n\r\n\r\n<div class=\"bb-main-content\">\r\n    <table class=\"bb-box\" cellpadding=\"0\" cellspacing=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; background: #ffffff; border-radius: 4px; -webkit-box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05); box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05); border: 1px solid #dce0e5; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\r\n        <tbody>\r\n            <tr>\r\n                <td class=\"bb-content bb-pb-0\" align=\"center\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 40px 48px; padding-bottom: 0;\">\r\n                    <table class=\"bb-icon bb-icon-lg bb-bg-blue\" cellspacing=\"0\" cellpadding=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 0; border-radius: 50%; background: #edeef0; line-height: 100%; font-weight: 300; border-collapse: separate; text-align: center; width: 72px; height: 72px; font-size: 48px; background-color: #206bc4; color: #ffffff; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\r\n                        <tbody>\r\n                            <tr>\r\n                                <td valign=\"middle\" align=\"center\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;\">\r\n                                    <img src=\"https://martfury.gc/vendor/core/core/base/images/email-icons/shopping-cart.png\" class=\"bb-va-middle\" width=\"40\" height=\"40\" alt=\"Icon\" style=\"border: 0 none; line-height: 100%; outline: none; text-decoration: none; font-size: 0; vertical-align: middle; display: block; width: 40px; height: 40px;\">\r\n                                </td>\r\n                            </tr>\r\n                        </tbody>\r\n                    </table>\r\n                    <h1 class=\"bb-text-center bb-m-0 bb-mt-md\" style=\"font-weight: 600; color: #232b42; font-size: 28px; line-height: 130%; text-align: center; margin: 0; margin-top: 16px;\">Order successfully!</h1>\r\n                </td>\r\n            </tr>\r\n            <tr>\r\n                <td class=\"bb-content\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 40px 48px;\">\r\n                    <div>Dear Young Shop,</div>\r\n                    <div>You got a new order on MartFury!</div>\r\n                </td>\r\n            </tr>\r\n            <tr>\r\n                <td class=\"bb-content bb-pt-0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 40px 48px; padding-top: 0;\">\r\n                    <table class=\"bb-row bb-mb-md\" cellpadding=\"0\" cellspacing=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; table-layout: fixed; margin-bottom: 16px; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\r\n                        <tbody>\r\n                            <tr>\r\n                                <td class=\"bb-bb-col\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;\">\r\n                                    <h4 class=\"bb-m-0\" style=\"font-weight: 600; color: #232b42; font-size: 16px; margin: 0;\">Customer Information</h4>\r\n                                    <div>Name: <strong style=\"font-weight: 600;\">Fae Koelpin</strong>\r\n</div>\r\n                                                                        <div>Phone: <strong style=\"font-weight: 600;\">+18598017656</strong>\r\n</div>\r\n                                                                                                                                                <div>Address: <strong style=\"font-weight: 600;\">2358 Jadon Stream, Bashirianfurt, Utah, PE</strong>\r\n</div>\r\n                                                                    </td>\r\n                            </tr>\r\n                        </tbody>\r\n                    </table>\r\n                </td>\r\n            </tr>\r\n            <tr>\r\n                <td class=\"bb-content bb-pt-0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 40px 48px; padding-top: 0;\">\r\n                    <h4 style=\"font-weight: 600; margin: 0 0 0.5em; color: #232b42; font-size: 16px;\">Here's what you ordered:</h4>\r\n                    <a class=\"button button-blue\" href=\"https://martfury.gc/orders/tracking?order_id=%2310000052&amp;email=vendor%40botble.com\" style=\"color: #206bc4; text-decoration: none;\">View order</a>\r\n    or <a href=\"https://martfury.gc\" style=\"color: #206bc4; text-decoration: none;\">Go to our shop</a>\r\n\r\n    <br>\r\n\r\n<table class=\"bb-table\" cellspacing=\"0\" cellpadding=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\r\n    <thead>\r\n        <tr>\r\n            <th colspan=\"2\" style=\"text-transform: uppercase; font-weight: 600; color: #667382; font-size: 12px; padding: 0 0 4px 0; padding-left: 0;\"></th>\r\n            <th style=\"text-transform: uppercase; font-weight: 600; color: #667382; font-size: 12px; padding: 0 0 4px 0;\">Quantity</th>\r\n            <th class=\"bb-text-right\" style=\"text-align: right; text-transform: uppercase; font-weight: 600; color: #667382; font-size: 12px; padding: 0 0 4px 0; padding-right: 0;\">Price</th>\r\n        </tr>\r\n    </thead>\r\n\r\n    <tbody>\r\n                <tr>\r\n            <td class=\"bb-pr-0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding-right: 0; padding: 4px 0; padding-left: 0;\">\r\n                <a href=\"\" style=\"color: #206bc4; text-decoration: none;\">\r\n                    <img src=\"https://martfury.gc/storage/products/6-150x150.jpg\" class=\" bb-rounded\" width=\"64\" height=\"64\" alt=\"\" style=\"line-height: 100%; outline: none; text-decoration: none; vertical-align: baseline; font-size: 0; border: 0 none; border-radius: 4px;\">\r\n                </a>\r\n            </td>\r\n            <td class=\"bb-pl-md bb-w-100p\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; width: 100%; padding-left: 16px !important; padding: 4px 0;\">\r\n                <strong style=\"font-weight: 600;\">Nikon HD camera</strong><br>\r\n                                    <span class=\"bb-text-muted\" style=\"color: #667382;\">(Color: Black, Size: XXL)</span>\r\n                \r\n                            </td>\r\n            <td class=\"bb-text-center\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: center; padding: 4px 0;\">x 1</td>\r\n            <td class=\"bb-text-right\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right; padding: 4px 0; padding-right: 0;\">SAR480.00</td>\r\n        </tr>\r\n    \r\n                                    <tr>\r\n                    <td colspan=\"2\" class=\"bb-border-top bb-text-right\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right; border-top: 1px solid #dce0e5; padding: 4px 0; padding-left: 0;\">Subtotal</td>\r\n                    <td colspan=\"2\" class=\"bb-border-top bb-text-right\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right; border-top: 1px solid #dce0e5; padding: 4px 0; padding-right: 0;\">SAR480.00</td>\r\n                </tr>\r\n            \r\n            \r\n                            <tr>\r\n                    <td colspan=\"2\" class=\"bb-text-right\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right; padding: 4px 0; padding-left: 0;\">Tax</td>\r\n                    <td colspan=\"2\" class=\"bb-text-right\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right; padding: 4px 0; padding-right: 0;\">SAR48.00</td>\r\n                </tr>\r\n            \r\n            \r\n                        <tr>\r\n                <td colspan=\"2\" class=\"bb-text-right bb-font-strong bb-h3 bb-m-0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; color: #232b42; font-size: 20px; line-height: 130%; text-align: right; font-weight: 600; margin: 0; padding: 4px 0; padding-left: 0;\">Total</td>\r\n                <td colspan=\"2\" class=\"bb-font-strong bb-h3 bb-m-0 bb-text-right\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; color: #232b42; font-size: 20px; line-height: 130%; text-align: right; font-weight: 600; margin: 0; padding: 4px 0; padding-right: 0;\">SAR528.00</td>\r\n            </tr>\r\n            </tbody>\r\n</table>\r\n\r\n\r\n                                    </td>\r\n            </tr>\r\n            <tr>\r\n                <td class=\"bb-content bb-border-top\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 40px 48px; border-top: 1px solid #dce0e5;\">\r\n                    <table class=\"bb-row bb-mb-md\" cellpadding=\"0\" cellspacing=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; table-layout: fixed; margin-bottom: 16px; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\r\n                        <tbody>\r\n                            <tr>\r\n                                <td class=\"bb-bb-col\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;\">\r\n                                    <h4 class=\"bb-m-0\" style=\"font-weight: 600; color: #232b42; font-size: 16px; margin: 0;\">Order number</h4>\r\n                                    <div>#10000052</div>\r\n                                </td>\r\n                                <td class=\"bb-col-spacer\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; vertical-align: top; width: 24px;\"></td>\r\n                                <td class=\"bb-col\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; vertical-align: top;\">\r\n                                    <h4 class=\"bb-mb-0\" style=\"font-weight: 600; margin: 0 0 0.5em; color: #232b42; font-size: 16px; margin-bottom: 0;\">Order date</h4>\r\n                                    <div>2025-08-15 16:37:22</div>\r\n                                </td>\r\n                            </tr>\r\n                        </tbody>\r\n                    </table>\r\n                    <table class=\"bb-row\" cellpadding=\"0\" cellspacing=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; table-layout: fixed; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\r\n                        <tbody>\r\n                            <tr>\r\n                                <td class=\"bb-col\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; vertical-align: top;\">\r\n                                                                        <h4 class=\"bb-m-0\" style=\"font-weight: 600; color: #232b42; font-size: 16px; margin: 0;\">Shipping Method</h4>\r\n                                    <div>\r\n                                        Local Pickup\r\n                                    </div>\r\n                                                                    </td>\r\n\r\n                                <td class=\"bb-col-spacer\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; vertical-align: top; width: 24px;\"></td>\r\n                                <td class=\"bb-col\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; vertical-align: top;\">\r\n                                    <h4 class=\"bb-m-0\" style=\"font-weight: 600; color: #232b42; font-size: 16px; margin: 0;\">Payment Method</h4>\r\n                                    <div>\r\n                                        HyperPay\r\n                                    </div>\r\n                                </td>\r\n                            </tr>\r\n                        </tbody>\r\n                    </table>\r\n                </td>\r\n            </tr>\r\n        </tbody>\r\n    </table>\r\n</div>\r\n\r\n<table cellspacing=\"0\" cellpadding=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\r\n    <tbody>\r\n        <tr>\r\n            <td class=\"bb-py-xl\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding-top: 48px; padding-bottom: 48px;\">\r\n                <table class=\"bb-text-center bb-text-muted\" cellspacing=\"0\" cellpadding=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; color: #667382; text-align: center; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\r\n                    <tbody>\r\n                    \r\n                    <tr>\r\n                        <td class=\"bb-px-lg\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding-right: 24px; padding-left: 24px;\">\r\n                            © 2025 MartFury. All Rights Reserved.\r\n                        </td>\r\n                    </tr>\r\n\r\n                                            <tr>\r\n                            <td class=\"bb-pt-md\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding-top: 16px;\">\r\n                                If you have any questions, feel free to message us at <a href=\"mailto:<EMAIL>\" style=\"color: #206bc4; text-decoration: none;\"><EMAIL></a>.\r\n                            </td>\r\n                        </tr>\r\n                                        </tbody>\r\n                </table>\r\n            </td>\r\n        </tr>\r\n    </tbody>\r\n</table>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table>\r\n</center>\r\n</body>\r\n\r\n</html>", "message_html": null, "is_string": false, "label": "debug", "time": **********.637311, "xdebug_link": null, "collector": "log"}, {"message": "[16:38:06] LOG.info: HyperPay Checkout Token Retrieved {\n    \"order_id\": 53,\n    \"checkout_token\": \"2bbdbd08f9ec9fc36d8b3e7ced0cf369\",\n    \"payment_result\": null\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.668806, "xdebug_link": null, "collector": "log"}, {"message": "[16:38:06] LOG.error: HyperPay Payment Failed {\n    \"order_id\": 53,\n    \"checkout_token\": \"2bbdbd08f9ec9fc36d8b3e7ced0cf369\",\n    \"error_message\": \"Payment failed: invalid or missing parameter (Code: 200.300.404)\",\n    \"resource_path\": \"\\/v1\\/checkouts\\/460F3D223F84416536B101539705D3A8.uat01-vm-tx02\\/payment\",\n    \"checkout_id\": \"460F3D223F84416536B101539705D3A8.uat01-vm-tx02\"\n}", "message_html": null, "is_string": false, "label": "error", "time": **********.669032, "xdebug_link": null, "collector": "log"}]}, "time": {"count": 9, "start": *********4.262628, "end": **********.674247, "duration": 2.411618947982788, "duration_str": "2.41s", "measures": [{"label": "Booting", "start": *********4.262628, "relative_start": 0, "end": *********4.967398, "relative_end": *********4.967398, "duration": 0.****************, "duration_str": "705ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": *********4.967408, "relative_start": 0.***************, "end": **********.674249, "relative_end": 1.9073486328125e-06, "duration": 1.***************, "duration_str": "1.71s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": *********4.984231, "relative_start": 0.****************, "end": *********4.995993, "relative_end": *********4.995993, "duration": 0.011761903762817383, "duration_str": "11.76ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: plugins/marketplace::emails.partials.order-detail", "start": **********.107824, "relative_start": 1.***************, "end": **********.107824, "relative_end": **********.107824, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::emails.partials.order-detail", "start": **********.1091, "relative_start": 1.****************, "end": **********.1091, "relative_end": **********.1091, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::emails.partials.order-detail", "start": **********.136798, "relative_start": 1.8741698265075684, "end": **********.136798, "relative_end": **********.136798, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::emails.partials.order-delivery-notes", "start": **********.143508, "relative_start": 1.8808798789978027, "end": **********.143508, "relative_end": **********.143508, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "mail: New order(s) at MartFury", "start": **********.630008, "relative_start": 2.367379903793335, "end": **********.638087, "relative_end": **********.638087, "duration": 0.008079051971435547, "duration_str": "8.08ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "mail", "group": null}, {"label": "Preparing Response", "start": **********.670721, "relative_start": 2.408092975616455, "end": **********.67118, "relative_end": **********.67118, "duration": 0.0004589557647705078, "duration_str": "459μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 63199272, "peak_usage_str": "60MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.21.0", "PHP Version": "8.3.17", "Environment": "local", "Debug Mode": "Enabled", "URL": "martfury.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 4, "nb_templates": 4, "templates": [{"name": "plugins/marketplace::emails.partials.order-detail", "param_count": null, "params": [], "start": **********.107786, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/plugins/marketplace/resources/views/emails/partials/order-detail.blade.phpplugins/marketplace::emails.partials.order-detail", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fmarketplace%2Fresources%2Fviews%2Femails%2Fpartials%2Forder-detail.blade.php&line=1", "ajax": false, "filename": "order-detail.blade.php", "line": "?"}}, {"name": "plugins/ecommerce::emails.partials.order-detail", "param_count": null, "params": [], "start": **********.109081, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/plugins/ecommerce/resources/views/emails/partials/order-detail.blade.phpplugins/ecommerce::emails.partials.order-detail", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Femails%2Fpartials%2Forder-detail.blade.php&line=1", "ajax": false, "filename": "order-detail.blade.php", "line": "?"}}, {"name": "plugins/ecommerce::emails.partials.order-detail", "param_count": null, "params": [], "start": **********.136776, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/plugins/ecommerce/resources/views/emails/partials/order-detail.blade.phpplugins/ecommerce::emails.partials.order-detail", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Femails%2Fpartials%2Forder-detail.blade.php&line=1", "ajax": false, "filename": "order-detail.blade.php", "line": "?"}}, {"name": "plugins/ecommerce::emails.partials.order-delivery-notes", "param_count": null, "params": [], "start": **********.143484, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/plugins/ecommerce/resources/views/emails/partials/order-delivery-notes.blade.phpplugins/ecommerce::emails.partials.order-delivery-notes", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Femails%2Fpartials%2Forder-delivery-notes.blade.php&line=1", "ajax": false, "filename": "order-delivery-notes.blade.php", "line": "?"}}]}, "queries": {"count": 63, "nb_statements": 63, "nb_visible_statements": 63, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.13593, "accumulated_duration_str": "136ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Footprints/TrackingFilter.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Footprints\\TrackingFilter.php", "line": 45}], "start": **********.014528, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 0, "width_percent": 0.397}, {"sql": "select * from `ec_orders` where `id` in (53)", "type": "query", "params": [], "bindings": [53], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 309}, {"index": 20, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 46}, {"index": 22, "namespace": null, "name": "platform/plugins/hyperpay/src/Http/Controllers/HyperPayController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\hyperpay\\src\\Http\\Controllers\\HyperPayController.php", "line": 84}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.770702, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 0.397, "width_percent": 1.067}, {"sql": "select * from `ec_currencies` where `is_default` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/CurrencySupport.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\CurrencySupport.php", "line": 74}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 311}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 46}, {"index": 24, "namespace": null, "name": "platform/plugins/hyperpay/src/Http/Controllers/HyperPayController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\hyperpay\\src\\Http\\Controllers\\HyperPayController.php", "line": 84}], "start": **********.776551, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 1.464, "width_percent": 0.375}, {"sql": "select * from `payments` where `charge_id` = '460F3D223F84416536B101539705D3A8.uat01-vm-tx02' and `order_id` in (53) limit 1", "type": "query", "params": [], "bindings": ["460F3D223F84416536B101539705D3A8.uat01-vm-tx02", 53], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/payment/src/Supports/PaymentHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\payment\\src\\Supports\\PaymentHelper.php", "line": 39}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 318}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 46}, {"index": 24, "namespace": null, "name": "platform/plugins/hyperpay/src/Http/Controllers/HyperPayController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\hyperpay\\src\\Http\\Controllers\\HyperPayController.php", "line": 84}], "start": **********.779447, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 1.839, "width_percent": 0.655}, {"sql": "insert into `payments` (`amount`, `payment_fee`, `currency`, `charge_id`, `order_id`, `customer_id`, `customer_type`, `payment_channel`, `status`, `updated_at`, `created_at`) values ('528.00', 0, 'SAR', '460F3D223F84416536B101539705D3A8.uat01-vm-tx02', 53, 2, 'Botble\\\\Ecommerce\\\\Models\\\\Customer', 'hyperpay', 'failed', '2025-08-15 16:38:05', '2025-08-15 16:38:05')", "type": "query", "params": [], "bindings": ["528.00", 0, "SAR", "460F3D223F84416536B101539705D3A8.uat01-vm-tx02", 53, 2, "Botble\\Ecommerce\\Models\\Customer", {"value": "hyperpay", "label": "HyperPay"}, {"value": "failed", "label": "Failed"}, "2025-08-15 16:38:05", "2025-08-15 16:38:05"], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "platform/plugins/payment/src/Supports/PaymentHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\payment\\src\\Supports\\PaymentHelper.php", "line": 60}, {"index": 19, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 318}, {"index": 23, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 46}, {"index": 25, "namespace": null, "name": "platform/plugins/hyperpay/src/Http/Controllers/HyperPayController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\hyperpay\\src\\Http\\Controllers\\HyperPayController.php", "line": 84}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.784994, "duration": 0.00375, "duration_str": "3.75ms", "memory": 0, "memory_str": null, "filename": "PaymentHelper.php:60", "source": {"index": 18, "namespace": null, "name": "platform/plugins/payment/src/Supports/PaymentHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\payment\\src\\Supports\\PaymentHelper.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fpayment%2Fsrc%2FSupports%2FPaymentHelper.php&line=60", "ajax": false, "filename": "PaymentHelper.php", "line": "60"}, "connection": "martfury", "explain": null, "start_percent": 2.494, "width_percent": 2.759}, {"sql": "select * from `ec_orders` where `id` in (53)", "type": "query", "params": [], "bindings": [53], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 73}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 321}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 46}, {"index": 24, "namespace": null, "name": "platform/plugins/hyperpay/src/Http/Controllers/HyperPayController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\hyperpay\\src\\Http\\Controllers\\HyperPayController.php", "line": 84}], "start": **********.795881, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 5.253, "width_percent": 0.39}, {"sql": "select * from `payments` where `charge_id` = '460F3D223F84416536B101539705D3A8.uat01-vm-tx02' and `order_id` in (53)", "type": "query", "params": [], "bindings": ["460F3D223F84416536B101539705D3A8.uat01-vm-tx02", 53], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 83}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 321}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 46}, {"index": 24, "namespace": null, "name": "platform/plugins/hyperpay/src/Http/Controllers/HyperPayController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\hyperpay\\src\\Http\\Controllers\\HyperPayController.php", "line": 84}], "start": **********.797838, "duration": 0.00394, "duration_str": "3.94ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 5.643, "width_percent": 2.899}, {"sql": "update `ec_orders` set `payment_id` = 93, `ec_orders`.`updated_at` = '2025-08-15 16:38:05' where `id` = 53", "type": "query", "params": [], "bindings": [93, "2025-08-15 16:38:05", 53], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 90}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 321}, {"index": 20, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 46}, {"index": 22, "namespace": null, "name": "platform/plugins/hyperpay/src/Http/Controllers/HyperPayController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\hyperpay\\src\\Http\\Controllers\\HyperPayController.php", "line": 84}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.8048398, "duration": 0.00425, "duration_str": "4.25ms", "memory": 0, "memory_str": null, "filename": "OrderHelper.php:90", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FSupports%2FOrderHelper.php&line=90", "ajax": false, "filename": "OrderHelper.php", "line": "90"}, "connection": "martfury", "explain": null, "start_percent": 8.541, "width_percent": 3.127}, {"sql": "select * from `ec_orders` where `ec_orders`.`id` = 53 limit 1", "type": "query", "params": [], "bindings": [53], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 110}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 63}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesModels.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesModels.php", "line": 97}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php", "line": 62}], "start": **********.868289, "duration": 0.0038399999999999997, "duration_str": "3.84ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 11.668, "width_percent": 2.825}, {"sql": "select exists(select * from `ec_invoices` where `ec_invoices`.`reference_id` = 53 and `ec_invoices`.`reference_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [53], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/InvoiceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\InvoiceHelper.php", "line": 31}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/GenerateInvoiceListener.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\GenerateInvoiceListener.php", "line": 16}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}], "start": **********.880936, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "InvoiceHelper.php:31", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/InvoiceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\InvoiceHelper.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FSupports%2FInvoiceHelper.php&line=31", "ajax": false, "filename": "InvoiceHelper.php", "line": "31"}, "connection": "martfury", "explain": null, "start_percent": 14.493, "width_percent": 0.655}, {"sql": "select * from `ec_order_addresses` where `ec_order_addresses`.`order_id` = 53 and `ec_order_addresses`.`order_id` is not null and `type` = 'shipping_address' limit 1", "type": "query", "params": [], "bindings": [53, "shipping_address"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/InvoiceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\InvoiceHelper.php", "line": 35}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/GenerateInvoiceListener.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\GenerateInvoiceListener.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.883812, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 15.148, "width_percent": 0.868}, {"sql": "select * from `ec_order_tax_information` where `ec_order_tax_information`.`order_id` = 53 and `ec_order_tax_information`.`order_id` is not null limit 1", "type": "query", "params": [], "bindings": [53], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/InvoiceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\InvoiceHelper.php", "line": 41}, {"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/GenerateInvoiceListener.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\GenerateInvoiceListener.php", "line": 16}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.8870878, "duration": 0.00254, "duration_str": "2.54ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 16.016, "width_percent": 1.869}, {"sql": "select exists(select * from `countries`) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 597}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Traits/LocationTrait.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Traits\\LocationTrait.php", "line": 74}, {"index": 19, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 20, "namespace": null, "name": "platform/plugins/ecommerce/src/Traits/LocationTrait.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Traits\\LocationTrait.php", "line": 94}, {"index": 27, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.891459, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "EcommerceHelper.php:597", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 597}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FSupports%2FEcommerceHelper.php&line=597", "ajax": false, "filename": "EcommerceHelper.php", "line": "597"}, "connection": "martfury", "explain": null, "start_percent": 17.884, "width_percent": 0.221}, {"sql": "select * from `payments` where `payments`.`id` = 93 limit 1", "type": "query", "params": [], "bindings": [93], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/InvoiceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\InvoiceHelper.php", "line": 76}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/GenerateInvoiceListener.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\GenerateInvoiceListener.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.895879, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 18.105, "width_percent": 0.596}, {"sql": "select max(`id`) as aggregate from `ec_invoices`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Invoice.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Invoice.php", "line": 84}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Invoice.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Invoice.php", "line": 58}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/InvoiceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\InvoiceHelper.php", "line": 86}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/GenerateInvoiceListener.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\GenerateInvoiceListener.php", "line": 16}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.9020839, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "Invoice.php:84", "source": {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Invoice.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Invoice.php", "line": 84}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FInvoice.php&line=84", "ajax": false, "filename": "Invoice.php", "line": "84"}, "connection": "martfury", "explain": null, "start_percent": 18.701, "width_percent": 0.537}, {"sql": "select exists(select * from `ec_invoices` where `code` = 'INV-42') as `exists`", "type": "query", "params": [], "bindings": ["INV-42"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Invoice.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Invoice.php", "line": 89}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Invoice.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Invoice.php", "line": 58}, {"index": 20, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/InvoiceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\InvoiceHelper.php", "line": 86}, {"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/GenerateInvoiceListener.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\GenerateInvoiceListener.php", "line": 16}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.903459, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "Invoice.php:89", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Invoice.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Invoice.php", "line": 89}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FInvoice.php&line=89", "ajax": false, "filename": "Invoice.php", "line": "89"}, "connection": "martfury", "explain": null, "start_percent": 19.238, "width_percent": 0.522}, {"sql": "insert into `ec_invoices` (`reference_id`, `reference_type`, `company_name`, `company_logo`, `customer_name`, `customer_email`, `customer_phone`, `customer_address`, `customer_tax_id`, `payment_id`, `status`, `paid_at`, `tax_amount`, `shipping_amount`, `payment_fee`, `discount_amount`, `sub_total`, `amount`, `shipping_method`, `shipping_option`, `coupon_code`, `discount_description`, `description`, `created_at`, `code`, `updated_at`) values (53, 'Botble\\\\Ecommerce\\\\Models\\\\Order', '', null, 'Fae Koelpin', '<EMAIL>', '+13198941196', '2358 Jadon Stream, Bashirianfurt, Utah, PE', null, 93, 'failed', null, '48.00', '0.00', '0.00', '0.00', '480.00', '528.00', 'default', '3', null, null, null, '2025-08-15 16:37:22', 'INV-42', '2025-08-15 16:38:05')", "type": "query", "params": [], "bindings": [53, "Botble\\Ecommerce\\Models\\Order", "", null, "<PERSON><PERSON>", "<EMAIL>", "+13198941196", "2358 Jadon Stream, Bashirianfurt, Utah, PE", null, 93, {"value": "failed", "label": "Failed"}, null, "48.00", "0.00", "0.00", "0.00", "480.00", "528.00", {"value": "default", "label": "<PERSON><PERSON><PERSON>"}, "3", null, null, null, "2025-08-15 16:37:22", "INV-42", "2025-08-15 16:38:05"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/InvoiceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\InvoiceHelper.php", "line": 86}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/GenerateInvoiceListener.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\GenerateInvoiceListener.php", "line": 16}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}], "start": **********.905575, "duration": 0.0041600000000000005, "duration_str": "4.16ms", "memory": 0, "memory_str": null, "filename": "InvoiceHelper.php:86", "source": {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/InvoiceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\InvoiceHelper.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FSupports%2FInvoiceHelper.php&line=86", "ajax": false, "filename": "InvoiceHelper.php", "line": "86"}, "connection": "martfury", "explain": null, "start_percent": 19.76, "width_percent": 3.06}, {"sql": "select * from `ec_order_product` where `ec_order_product`.`order_id` = 53 and `ec_order_product`.`order_id` is not null", "type": "query", "params": [], "bindings": [53], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/InvoiceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\InvoiceHelper.php", "line": 88}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/GenerateInvoiceListener.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\GenerateInvoiceListener.php", "line": 16}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.912299, "duration": 0.00375, "duration_str": "3.75ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 22.821, "width_percent": 2.759}, {"sql": "select * from `ec_products` where `ec_products`.`id` in (38) and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published'))", "type": "query", "params": [], "bindings": [1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 27, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 28, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/InvoiceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\InvoiceHelper.php", "line": 88}, {"index": 30, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/GenerateInvoiceListener.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\GenerateInvoiceListener.php", "line": 16}], "start": **********.918308, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 25.579, "width_percent": 0.353}, {"sql": "insert into `ec_invoice_items` (`reference_id`, `reference_type`, `name`, `description`, `image`, `qty`, `price`, `sub_total`, `tax_amount`, `discount_amount`, `amount`, `options`, `invoice_id`, `updated_at`, `created_at`) values (38, 'Botble\\\\Ecommerce\\\\Models\\\\Product', 'Nikon HD camera', null, 'products/6.jpg', 1, '480.00', 480, '48.00', 0, 528, '{\\\"image\\\":\\\"products\\\\/6.jpg\\\",\\\"attributes\\\":\\\"(Color: Black, Size: XXL)\\\",\\\"taxRate\\\":10,\\\"taxClasses\\\":{\\\"VAT\\\":10},\\\"options\\\":[],\\\"extras\\\":[],\\\"sku\\\":\\\"SW-125-A0\\\",\\\"weight\\\":886}', 44, '2025-08-15 16:38:05', '2025-08-15 16:38:05')", "type": "query", "params": [], "bindings": [38, "Botble\\Ecommerce\\Models\\Product", "Nikon HD camera", null, "products/6.jpg", 1, "480.00", 480, "48.00", 0, 528, "{\"image\":\"products\\/6.jpg\",\"attributes\":\"(Color: Black, Size: XXL)\",\"taxRate\":10,\"taxClasses\":{\"VAT\":10},\"options\":[],\"extras\":[],\"sku\":\"SW-125-A0\",\"weight\":886}", 44, "2025-08-15 16:38:05", "2025-08-15 16:38:05"], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/InvoiceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\InvoiceHelper.php", "line": 89}, {"index": 20, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/GenerateInvoiceListener.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\GenerateInvoiceListener.php", "line": 16}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}], "start": **********.926274, "duration": 0.011980000000000001, "duration_str": "11.98ms", "memory": 0, "memory_str": null, "filename": "InvoiceHelper.php:89", "source": {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/InvoiceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\InvoiceHelper.php", "line": 89}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FSupports%2FInvoiceHelper.php&line=89", "ajax": false, "filename": "InvoiceHelper.php", "line": "89"}, "connection": "martfury", "explain": null, "start_percent": 25.932, "width_percent": 8.813}, {"sql": "select * from `ec_order_addresses` where `ec_order_addresses`.`order_id` = 53 and `ec_order_addresses`.`order_id` is not null and `type` = 'shipping_address' limit 1", "type": "query", "params": [], "bindings": [53, "shipping_address"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/OrderCreatedNotification.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\OrderCreatedNotification.php", "line": 18}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 508}, {"index": 30, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 321}], "start": **********.954005, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 34.746, "width_percent": 0.39}, {"sql": "select * from `ec_order_product` where `ec_order_product`.`order_id` = 53 and `ec_order_product`.`order_id` is not null", "type": "query", "params": [], "bindings": [53], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/OrderCreatedNotification.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\OrderCreatedNotification.php", "line": 19}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 508}, {"index": 29, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 321}], "start": **********.956854, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 35.136, "width_percent": 0.331}, {"sql": "select * from `ec_products` where `ec_products`.`id` in (38) and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published'))", "type": "query", "params": [], "bindings": [1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 27, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 28, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/OrderCreatedNotification.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\OrderCreatedNotification.php", "line": 19}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 508}], "start": **********.959081, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 35.467, "width_percent": 0.375}, {"sql": "insert into `admin_notifications` (`title`, `action_label`, `action_url`, `description`, `permission`, `updated_at`, `created_at`) values ('New order #10000052', 'View', '/admin/ecommerce/orders/edit/53', '<PERSON><PERSON> ordered 1 product(s)', '', '2025-08-15 16:38:05', '2025-08-15 16:38:05')", "type": "query", "params": [], "bindings": ["New order #10000052", "View", "/admin/ecommerce/orders/edit/53", "Fae Koelpin ordered 1 product(s)", "", "2025-08-15 16:38:05", "2025-08-15 16:38:05"], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "vendor/botble/platform/base/src/Listeners/AdminNotificationListener.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Listeners\\AdminNotificationListener.php", "line": 22}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 508}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 508}, {"index": 30, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 321}, {"index": 34, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 46}], "start": **********.968173, "duration": 0.004019999999999999, "duration_str": "4.02ms", "memory": 0, "memory_str": null, "filename": "AdminNotificationListener.php:22", "source": {"index": 18, "namespace": null, "name": "vendor/botble/platform/base/src/Listeners/AdminNotificationListener.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Listeners\\AdminNotificationListener.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FListeners%2FAdminNotificationListener.php&line=22", "ajax": false, "filename": "AdminNotificationListener.php", "line": "22"}, "connection": "martfury", "explain": null, "start_percent": 35.842, "width_percent": 2.957}, {"sql": "select * from `ec_customers` where `ec_customers`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/AbandonedCartService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\AbandonedCartService.php", "line": 58}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/MarkCartAsRecovered.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\MarkCartAsRecovered.php", "line": 17}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 508}], "start": **********.975089, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 38.799, "width_percent": 0.36}, {"sql": "select * from `ec_abandoned_carts` where `is_recovered` = 0 and (`customer_id` = 2) order by `created_at` desc limit 1", "type": "query", "params": [], "bindings": [0, 2], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/AbandonedCartService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\AbandonedCartService.php", "line": 71}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/MarkCartAsRecovered.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\MarkCartAsRecovered.php", "line": 17}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 508}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 321}], "start": **********.9779031, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 39.16, "width_percent": 0.324}, {"sql": "update `ec_orders` set `is_finished` = 1, `ec_orders`.`updated_at` = '2025-08-15 16:38:05' where `id` = 53", "type": "query", "params": [], "bindings": [1, "2025-08-15 16:38:05", 53], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 114}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 321}, {"index": 20, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 46}, {"index": 22, "namespace": null, "name": "platform/plugins/hyperpay/src/Http/Controllers/HyperPayController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\hyperpay\\src\\Http\\Controllers\\HyperPayController.php", "line": 84}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.9805899, "duration": 0.00411, "duration_str": "4.11ms", "memory": 0, "memory_str": null, "filename": "OrderHelper.php:114", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 114}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FSupports%2FOrderHelper.php&line=114", "ajax": false, "filename": "OrderHelper.php", "line": "114"}, "connection": "martfury", "explain": null, "start_percent": 39.484, "width_percent": 3.024}, {"sql": "select * from `ec_products` where `ec_products`.`id` = 38 and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published')) limit 1", "type": "query", "params": [], "bindings": [38, 1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 209}, {"index": 19, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 116}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 321}, {"index": 25, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 46}], "start": **********.987123, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 42.507, "width_percent": 0.419}, {"sql": "update `ec_products` set `quantity` = 17, `ec_products`.`updated_at` = '2025-08-15 16:38:05' where `id` = 38", "type": "query", "params": [], "bindings": [17, "2025-08-15 16:38:05", 38], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 218}, {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 116}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 321}, {"index": 21, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 46}, {"index": 23, "namespace": null, "name": "platform/plugins/hyperpay/src/Http/Controllers/HyperPayController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\hyperpay\\src\\Http\\Controllers\\HyperPayController.php", "line": 84}], "start": **********.991539, "duration": 0.0202, "duration_str": "20.2ms", "memory": 0, "memory_str": null, "filename": "OrderHelper.php:218", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 218}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FSupports%2FOrderHelper.php&line=218", "ajax": false, "filename": "OrderHelper.php", "line": "218"}, "connection": "martfury", "explain": null, "start_percent": 42.927, "width_percent": 14.861}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`product_id` = 38 and `ec_product_variations`.`product_id` is not null limit 1", "type": "query", "params": [], "bindings": [38], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 430}, {"index": 30, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 149}], "start": **********.014654, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 57.787, "width_percent": 0.5}, {"sql": "select * from `ec_products` where `ec_products`.`id` = 6 and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published')) limit 1", "type": "query", "params": [], "bindings": [6, 1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 430}, {"index": 30, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 149}], "start": **********.0172932, "duration": 0.0044800000000000005, "duration_str": "4.48ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 58.287, "width_percent": 3.296}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 6 and `ec_product_variations`.`configurable_product_id` is not null and `ec_product_variations`.`is_default` = 1 limit 1", "type": "query", "params": [], "bindings": [6, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 149}, {"index": 30, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 218}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 116}], "start": **********.0239758, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 61.583, "width_percent": 0.795}, {"sql": "update `ec_products` set `quantity` = 17, `stock_status` = 'in_stock', `ec_products`.`updated_at` = '2025-08-15 16:38:06' where `id` = 6", "type": "query", "params": [], "bindings": [17, {"value": "in_stock", "label": "In stock"}, "2025-08-15 16:38:06", 6], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/UpdateDefaultProductService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\UpdateDefaultProductService.php", "line": 37}, {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 150}, {"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 218}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 116}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 321}], "start": **********.037683, "duration": 0.00475, "duration_str": "4.75ms", "memory": 0, "memory_str": null, "filename": "UpdateDefaultProductService.php:37", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/UpdateDefaultProductService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\UpdateDefaultProductService.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FServices%2FProducts%2FUpdateDefaultProductService.php&line=37", "ajax": false, "filename": "UpdateDefaultProductService.php", "line": "37"}, "connection": "martfury", "explain": null, "start_percent": 62.378, "width_percent": 3.494}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 6 and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [6], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/UpdateProductStockStatus.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\UpdateProductStockStatus.php", "line": 21}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 508}, {"index": 20, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 156}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/UpdateDefaultProductService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\UpdateDefaultProductService.php", "line": 37}, {"index": 28, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 150}], "start": **********.046777, "duration": 0.00391, "duration_str": "3.91ms", "memory": 0, "memory_str": null, "filename": "UpdateProductStockStatus.php:21", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/UpdateProductStockStatus.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\UpdateProductStockStatus.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FListeners%2FUpdateProductStockStatus.php&line=21", "ajax": false, "filename": "UpdateProductStockStatus.php", "line": "21"}, "connection": "martfury", "explain": null, "start_percent": 65.872, "width_percent": 2.876}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 6 and `ec_product_variations`.`configurable_product_id` is not null", "type": "query", "params": [], "bindings": [6], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/UpdateProductStockStatus.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\UpdateProductStockStatus.php", "line": 39}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/UpdateProductStockStatus.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\UpdateProductStockStatus.php", "line": 22}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 508}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 156}], "start": **********.0515742, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 68.749, "width_percent": 0.324}, {"sql": "select * from `ec_products` where `ec_products`.`id` in (38) and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published'))", "type": "query", "params": [], "bindings": [1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/UpdateProductStockStatus.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\UpdateProductStockStatus.php", "line": 39}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/UpdateProductStockStatus.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\UpdateProductStockStatus.php", "line": 22}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 508}], "start": **********.0536501, "duration": 0.00173, "duration_str": "1.73ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 69.072, "width_percent": 1.273}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 6 and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [6], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 159}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/UpdateDefaultProductService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\UpdateDefaultProductService.php", "line": 37}, {"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 150}, {"index": 29, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 218}, {"index": 30, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 116}], "start": **********.058514, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Product.php:159", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 159}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=159", "ajax": false, "filename": "Product.php", "line": "159"}, "connection": "martfury", "explain": null, "start_percent": 70.345, "width_percent": 0.36}, {"sql": "select `product_id` from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 6 and `ec_product_variations`.`configurable_product_id` is not null", "type": "query", "params": [], "bindings": [6], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 161}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/UpdateDefaultProductService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\UpdateDefaultProductService.php", "line": 37}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 150}, {"index": 32, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 218}, {"index": 33, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 116}], "start": **********.059787, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "Product.php:161", "source": {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 161}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=161", "ajax": false, "filename": "Product.php", "line": "161"}, "connection": "martfury", "explain": null, "start_percent": 70.706, "width_percent": 0.655}, {"sql": "update `ec_products` set `name` = 'Nikon HD camera', `minimum_order_quantity` = 0, `maximum_order_quantity` = 0, `ec_products`.`updated_at` = '2025-08-15 16:38:06' where `id` in (38) and `is_variation` = 1 and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published'))", "type": "query", "params": [], "bindings": ["Nikon HD camera", 0, 0, "2025-08-15 16:38:06", 38, 1, 1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 163}, {"index": 19, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/UpdateDefaultProductService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\UpdateDefaultProductService.php", "line": 37}, {"index": 20, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 150}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 218}, {"index": 28, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 116}], "start": **********.063884, "duration": 0.00452, "duration_str": "4.52ms", "memory": 0, "memory_str": null, "filename": "Product.php:163", "source": {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 163}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=163", "ajax": false, "filename": "Product.php", "line": "163"}, "connection": "martfury", "explain": null, "start_percent": 71.36, "width_percent": 3.325}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 6 and `ec_product_variations`.`configurable_product_id` is not null", "type": "query", "params": [], "bindings": [6], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/UpdateProductStockStatus.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\UpdateProductStockStatus.php", "line": 39}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/UpdateProductStockStatus.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\UpdateProductStockStatus.php", "line": 34}, {"index": 19, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/UpdateProductStockStatus.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\UpdateProductStockStatus.php", "line": 17}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 508}], "start": **********.0748322, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 74.685, "width_percent": 0.324}, {"sql": "select * from `ec_products` where `ec_products`.`id` in (38) and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published'))", "type": "query", "params": [], "bindings": [1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/UpdateProductStockStatus.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\UpdateProductStockStatus.php", "line": 39}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/UpdateProductStockStatus.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\UpdateProductStockStatus.php", "line": 34}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/UpdateProductStockStatus.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\UpdateProductStockStatus.php", "line": 17}], "start": **********.077265, "duration": 0.004059999999999999, "duration_str": "4.06ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 75.009, "width_percent": 2.987}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 6 and `ec_product_variations`.`configurable_product_id` is not null", "type": "query", "params": [], "bindings": [6], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/UpdateProductStockStatus.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\UpdateProductStockStatus.php", "line": 39}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/UpdateProductStockStatus.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\UpdateProductStockStatus.php", "line": 34}, {"index": 19, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/UpdateProductStockStatus.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\UpdateProductStockStatus.php", "line": 17}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 508}], "start": **********.086163, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 77.996, "width_percent": 0.25}, {"sql": "select * from `ec_products` where `ec_products`.`id` in (38) and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published'))", "type": "query", "params": [], "bindings": [1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/UpdateProductStockStatus.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\UpdateProductStockStatus.php", "line": 39}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/UpdateProductStockStatus.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\UpdateProductStockStatus.php", "line": 34}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/UpdateProductStockStatus.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\UpdateProductStockStatus.php", "line": 17}], "start": **********.088243, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 78.246, "width_percent": 0.463}, {"sql": "select * from `mp_stores` where `mp_stores`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 709}, {"index": 24, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 737}, {"index": 25, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 693}], "start": **********.0942059, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 78.71, "width_percent": 0.581}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_type` = 'Botble\\\\Marketplace\\\\Models\\\\Store' and `slugs`.`reference_id` = 3 and `slugs`.`reference_id` is not null limit 1", "type": "query", "params": [], "bindings": ["Botble\\Marketplace\\Models\\Store", 3], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "vendor/botble/slug/src/Providers/SlugServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\slug\\src\\Providers\\SlugServiceProvider.php", "line": 118}, {"index": 29, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 25}, {"index": 30, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 712}], "start": **********.09841, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 79.291, "width_percent": 0.449}, {"sql": "select * from `ec_currencies` order by `order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/CurrencySupport.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\CurrencySupport.php", "line": 107}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/CurrencySupport.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\CurrencySupport.php", "line": 41}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/helpers/currencies.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\helpers\\currencies.php", "line": 141}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.118593, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 79.74, "width_percent": 0.272}, {"sql": "select * from `ec_shipping_rules` where `ec_shipping_rules`.`id` = '3' limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 474}, {"index": 20, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Order.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Order.php", "line": 94}, {"index": 27, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 28, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 722}], "start": **********.123036, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 80.012, "width_percent": 0.316}, {"sql": "select * from `payments` where `payments`.`id` = 93 limit 1", "type": "query", "params": [], "bindings": [93], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 723}, {"index": 24, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 737}, {"index": 25, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 693}], "start": **********.124975, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 80.328, "width_percent": 0.213}, {"sql": "insert into `ec_order_histories` (`action`, `description`, `order_id`, `updated_at`, `created_at`) values ('send_order_confirmation_email', 'The email confirmation was sent to customer', 53, '2025-08-15 16:38:06', '2025-08-15 16:38:06')", "type": "query", "params": [], "bindings": [{"value": "send_order_confirmation_email", "label": "send_order_confirmation_email"}, "The email confirmation was sent to customer", 53, "2025-08-15 16:38:06", "2025-08-15 16:38:06"], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 742}, {"index": 19, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 693}, {"index": 23, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 26, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 321}, {"index": 30, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 46}], "start": **********.1294231, "duration": 0.0039700000000000004, "duration_str": "3.97ms", "memory": 0, "memory_str": null, "filename": "OrderSupportServiceProvider.php:742", "source": {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 742}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FProviders%2FOrderSupportServiceProvider.php&line=742", "ajax": false, "filename": "OrderSupportServiceProvider.php", "line": "742"}, "connection": "martfury", "explain": null, "start_percent": 80.541, "width_percent": 2.921}, {"sql": "select * from `ec_shipping_rules` where `ec_shipping_rules`.`id` = '3' limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 474}, {"index": 20, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Order.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Order.php", "line": 94}, {"index": 27, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 28, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 281}], "start": **********.141402, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 83.462, "width_percent": 0.324}, {"sql": "select * from `ec_shipments` where `ec_shipments`.`order_id` = 53 and `ec_shipments`.`order_id` is not null limit 1", "type": "query", "params": [], "bindings": [53], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": "view", "name": "plugins/ecommerce::emails.partials.order-delivery-notes", "file": "D:\\laragon\\www\\martfury\\platform/plugins/ecommerce/resources/views/emails/partials/order-delivery-notes.blade.php", "line": 1}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}], "start": **********.144771, "duration": 0.004019999999999999, "duration_str": "4.02ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 83.786, "width_percent": 2.957}, {"sql": "select * from `ec_flash_sales` where date(`end_date`) >= '2025-08-15' and `status` = 'published' order by `created_at` desc", "type": "query", "params": [], "bindings": ["2025-08-15", "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/FlashSaleRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\FlashSaleRepository.php", "line": 35}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/FlashSaleSupport.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\FlashSaleSupport.php", "line": 52}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/FlashSaleSupport.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\FlashSaleSupport.php", "line": 22}, {"index": 19, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Concerns/ProductPrices.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Concerns\\ProductPrices.php", "line": 46}], "start": **********.1644409, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 86.743, "width_percent": 0.28}, {"sql": "select * from `ec_customers` where `id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/DiscountSupport.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\DiscountSupport.php", "line": 22}], "start": **********.1672208, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 87.023, "width_percent": 0.184}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`product_id` = 38 and `ec_product_variations`.`product_id` is not null limit 1", "type": "query", "params": [], "bindings": [38], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 430}, {"index": 30, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Concerns/ProductPrices.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Concerns\\ProductPrices.php", "line": 58}], "start": **********.169162, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 87.207, "width_percent": 0.154}, {"sql": "select * from `ec_products` where `ec_products`.`id` = 6 and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published')) limit 1", "type": "query", "params": [], "bindings": [6, 1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 430}, {"index": 30, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Concerns/ProductPrices.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Concerns\\ProductPrices.php", "line": 58}], "start": **********.171078, "duration": 0.00159, "duration_str": "1.59ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 87.361, "width_percent": 1.17}, {"sql": "select * from `ec_discounts` where `type` = 'promotion' and `start_date` <= '2025-08-15 16:38:06' and (`end_date` is null or `end_date` >= '2025-08-15 16:38:06') and (`target` in ('all-orders', 'amount-minimum-order') or (`target` in ('customer', 'group-products', 'products-by-category', 'specific-product', 'product-variant') and `product_quantity` = 1))", "type": "query", "params": [], "bindings": ["promotion", "2025-08-15 16:38:06", "2025-08-15 16:38:06", "all-orders", "amount-minimum-order", "customer", "group-products", "products-by-category", "specific-product", "product-variant", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/DiscountRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\DiscountRepository.php", "line": 50}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/DiscountSupport.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\DiscountSupport.php", "line": 116}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/DiscountSupport.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\DiscountSupport.php", "line": 42}, {"index": 19, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Concerns/ProductPrices.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Concerns\\ProductPrices.php", "line": 58}], "start": **********.175941, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 88.531, "width_percent": 0.78}, {"sql": "select * from `ec_order_addresses` where `ec_order_addresses`.`order_id` = 53 and `ec_order_addresses`.`order_id` is not null and `type` = 'shipping_address' limit 1", "type": "query", "params": [], "bindings": [53, "shipping_address"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 301}, {"index": 25, "namespace": null, "name": "platform/plugins/marketplace/src/Supports/MarketplaceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Supports\\MarketplaceHelper.php", "line": 114}, {"index": 26, "namespace": null, "name": "platform/plugins/marketplace/src/Supports/MarketplaceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Supports\\MarketplaceHelper.php", "line": 103}], "start": **********.200294, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 89.311, "width_percent": 0.302}, {"sql": "select * from `languages` where `lang_is_default` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 940}, {"index": 19, "namespace": null, "name": "platform/plugins/language/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\language\\src\\Providers\\HookServiceProvider.php", "line": 89}, {"index": 23, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 25, "namespace": null, "name": "vendor/botble/platform/base/src/Supports/Language.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Supports\\Language.php", "line": 497}], "start": **********.229622, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 89.612, "width_percent": 0.287}, {"sql": "select `lang_locale`, `lang_code`, `lang_name`, `lang_flag`, `lang_is_rtl` from `languages` order by `lang_order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 156}, {"index": 18, "namespace": null, "name": "platform/plugins/language/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\language\\src\\Providers\\HookServiceProvider.php", "line": 118}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 24, "namespace": null, "name": "vendor/botble/platform/base/src/Supports/Language.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Supports\\Language.php", "line": 499}], "start": **********.2330132, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 89.899, "width_percent": 0.206}, {"sql": "insert into `email_logs` (`from`, `to`, `cc`, `bcc`, `subject`, `html_body`, `text_body`, `raw_body`, `debug_info`, `updated_at`, `created_at`) values ('\\\"Example\\\" <<EMAIL>>', 'kamron.<PERSON><PERSON>@example.org', '', '', 'New order(s) at MartFury', '<!doctype html>\\n<html lang=\\\"en\\\">\\n\\n<head>\\n    <meta charset=\\\"UTF-8\\\">\\n    <title>MartFury</title>\\n</head>\\n\\n<body class=\\\"bb-bg-body\\\" dir=\\\"ltr\\\" style=\\\"margin: 0; padding: 0; font-size: 14px; line-height: 171.4285714286%; mso-line-height-rule: exactly; color: #3A4859; width: 100%; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale; -webkit-font-feature-settings: \\'cv02\\', \\'cv03\\', \\'cv04\\', \\'cv11\\'; font-feature-settings: \\'cv02\\', \\'cv03\\', \\'cv04\\', \\'cv11\\'; font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; background-color: #f6f7f9;\\\">\\n\\n<center>\\n    <table class=\\\"bb-main bb-bg-body\\\" width=\\\"100%\\\" cellspacing=\\\"0\\\" cellpadding=\\\"0\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; background-color: #f6f7f9;\\\">\\n        <tbody>\\n            <tr>\\n                <td align=\\\"center\\\" valign=\\\"top\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;\\\">\\n                    <table class=\\\"bb-wrap\\\" cellspacing=\\\"0\\\" cellpadding=\\\"0\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; max-width: 640px; text-align: left; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\\\">\\n                        <tbody>\\n                            <tr>\\n                                <td class=\\\"bb-p-sm\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 8px;\\\">\\n                                    <table cellpadding=\\\"0\\\" cellspacing=\\\"0\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\\\">\\n                                        <tbody>\\n                                            <tr>\\n                                                <td class=\\\"bb-py-lg\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding-top: 24px; padding-bottom: 24px;\\\">\\n                                                    <table cellspacing=\\\"0\\\" cellpadding=\\\"0\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\\\">\\n                                                        <tbody>\\n                                                            <tr>\\n                                                                <td class=\\\"bb-text-left\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: left;\\\">\\n                                                                    <a href=\\\"https://martfury.gc\\\" style=\\\"color: #206bc4; text-decoration: none;\\\">\\n                                                                        <img class=\\\"bb-logo\\\" src=\\\"https://martfury.gc/storage/general/logo.png\\\" alt=\\\"MartFury\\\" style=\\\"line-height: 100%; outline: none; text-decoration: none; vertical-align: baseline; font-size: 0; border: 0 none; max-height: 40px;\\\">\\n                                                                    </a>\\n                                                                </td>\\n                                                                <td class=\\\"bb-text-right\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right;\\\">\\n                                                                    2025-08-15 16:38:06\\n                                                                </td>\\n                                                            </tr>\\n                                                        </tbody>\\n                                                    </table>\\n                                                </td>\\n                                            </tr>\\n                                        </tbody>\\n                                    </table>\\n\\n\\n<div class=\\\"bb-main-content\\\">\\n    <table class=\\\"bb-box\\\" cellpadding=\\\"0\\\" cellspacing=\\\"0\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; background: #ffffff; border-radius: 4px; -webkit-box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05); box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05); border: 1px solid #dce0e5; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\\\">\\n        <tbody>\\n            <tr>\\n                <td class=\\\"bb-content bb-pb-0\\\" align=\\\"center\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 40px 48px; padding-bottom: 0;\\\">\\n                    <table class=\\\"bb-icon bb-icon-lg bb-bg-blue\\\" cellspacing=\\\"0\\\" cellpadding=\\\"0\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 0; border-radius: 50%; background: #edeef0; line-height: 100%; font-weight: 300; border-collapse: separate; text-align: center; width: 72px; height: 72px; font-size: 48px; background-color: #206bc4; color: #ffffff; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\\\">\\n                        <tbody>\\n                            <tr>\\n                                <td valign=\\\"middle\\\" align=\\\"center\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;\\\">\\n                                    <img src=\\\"https://martfury.gc/vendor/core/core/base/images/email-icons/shopping-cart.png\\\" class=\\\"bb-va-middle\\\" width=\\\"40\\\" height=\\\"40\\\" alt=\\\"Icon\\\" style=\\\"border: 0 none; line-height: 100%; outline: none; text-decoration: none; font-size: 0; vertical-align: middle; display: block; width: 40px; height: 40px;\\\">\\n                                </td>\\n                            </tr>\\n                        </tbody>\\n                    </table>\\n                    <h1 class=\\\"bb-text-center bb-m-0 bb-mt-md\\\" style=\\\"font-weight: 600; color: #232b42; font-size: 28px; line-height: 130%; text-align: center; margin: 0; margin-top: 16px;\\\">Order successfully!</h1>\\n                </td>\\n            </tr>\\n            <tr>\\n                <td class=\\\"bb-content\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 40px 48px;\\\">\\n                    <div>Dear Young Shop,</div>\\n                    <div>You got a new order on MartFury!</div>\\n                </td>\\n            </tr>\\n            <tr>\\n                <td class=\\\"bb-content bb-pt-0\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 40px 48px; padding-top: 0;\\\">\\n                    <table class=\\\"bb-row bb-mb-md\\\" cellpadding=\\\"0\\\" cellspacing=\\\"0\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; table-layout: fixed; margin-bottom: 16px; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\\\">\\n                        <tbody>\\n                            <tr>\\n                                <td class=\\\"bb-bb-col\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;\\\">\\n                                    <h4 class=\\\"bb-m-0\\\" style=\\\"font-weight: 600; color: #232b42; font-size: 16px; margin: 0;\\\">Customer Information</h4>\\n                                    <div>Name: <strong style=\\\"font-weight: 600;\\\">Fae Koelpin</strong>\\n</div>\\n                                                                        <div>Phone: <strong style=\\\"font-weight: 600;\\\">+18598017656</strong>\\n</div>\\n                                                                                                                                                <div>Address: <strong style=\\\"font-weight: 600;\\\">2358 Jadon Stream, Bashirianfurt, Utah, PE</strong>\\n</div>\\n                                                                    </td>\\n                            </tr>\\n                        </tbody>\\n                    </table>\\n                </td>\\n            </tr>\\n            <tr>\\n                <td class=\\\"bb-content bb-pt-0\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 40px 48px; padding-top: 0;\\\">\\n                    <h4 style=\\\"font-weight: 600; margin: 0 0 0.5em; color: #232b42; font-size: 16px;\\\">Here\\'s what you ordered:</h4>\\n                    <a class=\\\"button button-blue\\\" href=\\\"https://martfury.gc/orders/tracking?order_id=%2310000052&amp;email=vendor%40botble.com\\\" style=\\\"color: #206bc4; text-decoration: none;\\\">View order</a>\\n    or <a href=\\\"https://martfury.gc\\\" style=\\\"color: #206bc4; text-decoration: none;\\\">Go to our shop</a>\\n\\n    <br>\\n\\n<table class=\\\"bb-table\\\" cellspacing=\\\"0\\\" cellpadding=\\\"0\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\\\">\\n    <thead>\\n        <tr>\\n            <th colspan=\\\"2\\\" style=\\\"text-transform: uppercase; font-weight: 600; color: #667382; font-size: 12px; padding: 0 0 4px 0; padding-left: 0;\\\"></th>\\n            <th style=\\\"text-transform: uppercase; font-weight: 600; color: #667382; font-size: 12px; padding: 0 0 4px 0;\\\">Quantity</th>\\n            <th class=\\\"bb-text-right\\\" style=\\\"text-align: right; text-transform: uppercase; font-weight: 600; color: #667382; font-size: 12px; padding: 0 0 4px 0; padding-right: 0;\\\">Price</th>\\n        </tr>\\n    </thead>\\n\\n    <tbody>\\n                <tr>\\n            <td class=\\\"bb-pr-0\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding-right: 0; padding: 4px 0; padding-left: 0;\\\">\\n                <a href=\\\"\\\" style=\\\"color: #206bc4; text-decoration: none;\\\">\\n                    <img src=\\\"https://martfury.gc/storage/products/6-150x150.jpg\\\" class=\\\" bb-rounded\\\" width=\\\"64\\\" height=\\\"64\\\" alt=\\\"\\\" style=\\\"line-height: 100%; outline: none; text-decoration: none; vertical-align: baseline; font-size: 0; border: 0 none; border-radius: 4px;\\\">\\n                </a>\\n            </td>\\n            <td class=\\\"bb-pl-md bb-w-100p\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; width: 100%; padding-left: 16px !important; padding: 4px 0;\\\">\\n                <strong style=\\\"font-weight: 600;\\\">Nikon HD camera</strong><br>\\n                                    <span class=\\\"bb-text-muted\\\" style=\\\"color: #667382;\\\">(Color: Black, Size: XXL)</span>\\n                \\n                            </td>\\n            <td class=\\\"bb-text-center\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: center; padding: 4px 0;\\\">x 1</td>\\n            <td class=\\\"bb-text-right\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right; padding: 4px 0; padding-right: 0;\\\">SAR480.00</td>\\n        </tr>\\n    \\n                                    <tr>\\n                    <td colspan=\\\"2\\\" class=\\\"bb-border-top bb-text-right\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right; border-top: 1px solid #dce0e5; padding: 4px 0; padding-left: 0;\\\">Subtotal</td>\\n                    <td colspan=\\\"2\\\" class=\\\"bb-border-top bb-text-right\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right; border-top: 1px solid #dce0e5; padding: 4px 0; padding-right: 0;\\\">SAR480.00</td>\\n                </tr>\\n            \\n            \\n                            <tr>\\n                    <td colspan=\\\"2\\\" class=\\\"bb-text-right\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right; padding: 4px 0; padding-left: 0;\\\">Tax</td>\\n                    <td colspan=\\\"2\\\" class=\\\"bb-text-right\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right; padding: 4px 0; padding-right: 0;\\\">SAR48.00</td>\\n                </tr>\\n            \\n            \\n                        <tr>\\n                <td colspan=\\\"2\\\" class=\\\"bb-text-right bb-font-strong bb-h3 bb-m-0\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; color: #232b42; font-size: 20px; line-height: 130%; text-align: right; font-weight: 600; margin: 0; padding: 4px 0; padding-left: 0;\\\">Total</td>\\n                <td colspan=\\\"2\\\" class=\\\"bb-font-strong bb-h3 bb-m-0 bb-text-right\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; color: #232b42; font-size: 20px; line-height: 130%; text-align: right; font-weight: 600; margin: 0; padding: 4px 0; padding-right: 0;\\\">SAR528.00</td>\\n            </tr>\\n            </tbody>\\n</table>\\n\\n\\n                                    </td>\\n            </tr>\\n            <tr>\\n                <td class=\\\"bb-content bb-border-top\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 40px 48px; border-top: 1px solid #dce0e5;\\\">\\n                    <table class=\\\"bb-row bb-mb-md\\\" cellpadding=\\\"0\\\" cellspacing=\\\"0\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; table-layout: fixed; margin-bottom: 16px; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\\\">\\n                        <tbody>\\n                            <tr>\\n                                <td class=\\\"bb-bb-col\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;\\\">\\n                                    <h4 class=\\\"bb-m-0\\\" style=\\\"font-weight: 600; color: #232b42; font-size: 16px; margin: 0;\\\">Order number</h4>\\n                                    <div>#10000052</div>\\n                                </td>\\n                                <td class=\\\"bb-col-spacer\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; vertical-align: top; width: 24px;\\\"></td>\\n                                <td class=\\\"bb-col\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; vertical-align: top;\\\">\\n                                    <h4 class=\\\"bb-mb-0\\\" style=\\\"font-weight: 600; margin: 0 0 0.5em; color: #232b42; font-size: 16px; margin-bottom: 0;\\\">Order date</h4>\\n                                    <div>2025-08-15 16:37:22</div>\\n                                </td>\\n                            </tr>\\n                        </tbody>\\n                    </table>\\n                    <table class=\\\"bb-row\\\" cellpadding=\\\"0\\\" cellspacing=\\\"0\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; table-layout: fixed; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\\\">\\n                        <tbody>\\n                            <tr>\\n                                <td class=\\\"bb-col\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; vertical-align: top;\\\">\\n                                                                        <h4 class=\\\"bb-m-0\\\" style=\\\"font-weight: 600; color: #232b42; font-size: 16px; margin: 0;\\\">Shipping Method</h4>\\n                                    <div>\\n                                        Local Pickup\\n                                    </div>\\n                                                                    </td>\\n\\n                                <td class=\\\"bb-col-spacer\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; vertical-align: top; width: 24px;\\\"></td>\\n                                <td class=\\\"bb-col\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; vertical-align: top;\\\">\\n                                    <h4 class=\\\"bb-m-0\\\" style=\\\"font-weight: 600; color: #232b42; font-size: 16px; margin: 0;\\\">Payment Method</h4>\\n                                    <div>\\n                                        HyperPay\\n                                    </div>\\n                                </td>\\n                            </tr>\\n                        </tbody>\\n                    </table>\\n                </td>\\n            </tr>\\n        </tbody>\\n    </table>\\n</div>\\n\\n<table cellspacing=\\\"0\\\" cellpadding=\\\"0\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\\\">\\n    <tbody>\\n        <tr>\\n            <td class=\\\"bb-py-xl\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding-top: 48px; padding-bottom: 48px;\\\">\\n                <table class=\\\"bb-text-center bb-text-muted\\\" cellspacing=\\\"0\\\" cellpadding=\\\"0\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; color: #667382; text-align: center; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\\\">\\n                    <tbody>\\n                    \\n                    <tr>\\n                        <td class=\\\"bb-px-lg\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding-right: 24px; padding-left: 24px;\\\">\\n                            © 2025 MartFury. All Rights Reserved.\\n                        </td>\\n                    </tr>\\n\\n                                            <tr>\\n                            <td class=\\\"bb-pt-md\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding-top: 16px;\\\">\\n                                If you have any questions, feel free to message us at <a href=\\\"mailto:<EMAIL>\\\" style=\\\"color: #206bc4; text-decoration: none;\\\"><EMAIL></a>.\\n                            </td>\\n                        </tr>\\n                                        </tbody>\\n                </table>\\n            </td>\\n        </tr>\\n    </tbody>\\n</table>\\n</td>\\n</tr>\\n</tbody>\\n</table>\\n</td>\\n</tr>\\n</tbody>\\n</table>\\n</center>\\n</body>\\n\\n</html>', null, 'From: Example <<EMAIL>>\\r\\nTo: <EMAIL>\\r\\nSubject: New order(s) at MartFury\\r\\nMessage-ID: <<EMAIL>>\\r\\nMIME-Version: 1.0\\r\\nDate: Fri, 15 Aug 2025 16:38:06 +0000\\r\\nContent-Type: text/html; charset=utf-8\\r\\nContent-Transfer-Encoding: quoted-printable\\r\\n\\r\\n<!doctype html>\\r\\n<html lang=3D\\\"en\\\">\\r\\n\\r\\n<head>\\r\\n    <meta charset=3D\\\"UTF=\\r\\n-8\\\">\\r\\n    <title>MartFury</title>\\r\\n</head>\\r\\n\\r\\n<body class=3D\\\"bb-bg-body=\\r\\n\\\" dir=3D\\\"ltr\\\" style=3D\\\"margin: 0; padding: 0; font-size: 14px; line-height:=\\r\\n 171.4285714286%; mso-line-height-rule: exactly; color: #3A4859; width: 100=\\r\\n%; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale;=\\r\\n -webkit-font-feature-settings: \\'cv02\\', \\'cv03\\', \\'cv04\\', \\'cv11\\'; font-featur=\\r\\ne-settings: \\'cv02\\', \\'cv03\\', \\'cv04\\', \\'cv11\\'; font-family: Inter, -apple-syst=\\r\\nem, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sa=\\r\\nns-serif; background-color: #f6f7f9;\\\">\\r\\n\\r\\n<center>\\r\\n    <table class=3D\\\"=\\r\\nbb-main bb-bg-body\\\" width=3D\\\"100%\\\" cellspacing=3D\\\"0\\\" cellpadding=3D\\\"0\\\" styl=\\r\\ne=3D\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, =\\r\\nSegoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; wi=\\r\\ndth: 100%; -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; back=\\r\\nground-color: #f6f7f9;\\\">\\r\\n        <tbody>\\r\\n            <tr>\\r\\n           =\\r\\n     <td align=3D\\\"center\\\" valign=3D\\\"top\\\" style=3D\\\"font-family: Inter, -appl=\\r\\ne-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Ne=\\r\\nue, sans-serif;\\\">\\r\\n                    <table class=3D\\\"bb-wrap\\\" cellspacin=\\r\\ng=3D\\\"0\\\" cellpadding=3D\\\"0\\\" style=3D\\\"font-family: Inter, -apple-system, Blink=\\r\\nMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;=\\r\\n border-collapse: collapse; width: 100%; max-width: 640px; text-align: left=\\r\\n; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\\\">\\r\\n               =\\r\\n         <tbody>\\r\\n                            <tr>\\r\\n                     =\\r\\n           <td class=3D\\\"bb-p-sm\\\" style=3D\\\"font-family: Inter, -apple-system=\\r\\n, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans=\\r\\n-serif; padding: 8px;\\\">\\r\\n                                    <table cellpa=\\r\\ndding=3D\\\"0\\\" cellspacing=3D\\\"0\\\" style=3D\\\"font-family: Inter, -apple-system, B=\\r\\nlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-se=\\r\\nrif; border-collapse: collapse; width: 100%; -premailer-cellpadding: 0; -pr=\\r\\nemailer-cellspacing: 0;\\\">\\r\\n                                        <tbody>=\\r\\n\\r\\n                                            <tr>\\r\\n                     =\\r\\n                           <td class=3D\\\"bb-py-lg\\\" style=3D\\\"font-family: Int=\\r\\ner, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Hel=\\r\\nvetica Neue, sans-serif; padding-top: 24px; padding-bottom: 24px;\\\">\\r\\n     =\\r\\n                                               <table cellspacing=3D\\\"0\\\" cel=\\r\\nlpadding=3D\\\"0\\\" style=3D\\\"font-family: Inter, -apple-system, BlinkMacSystemFo=\\r\\nnt, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-col=\\r\\nlapse: collapse; width: 100%; -premailer-cellpadding: 0; -premailer-cellspa=\\r\\ncing: 0;\\\">\\r\\n                                                        <tbody=\\r\\n>\\r\\n                                                            <tr>\\r\\n    =\\r\\n                                                            <td class=3D\\\"bb=\\r\\n-text-left\\\" style=3D\\\"font-family: Inter, -apple-system, BlinkMacSystemFont,=\\r\\n San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: l=\\r\\neft;\\\">\\r\\n                                                                  =\\r\\n  <a href=3D\\\"https://martfury.gc\\\" style=3D\\\"color: #206bc4; text-decoration:=\\r\\n none;\\\">\\r\\n                                                                =\\r\\n        <img class=3D\\\"bb-logo\\\" src=3D\\\"https://martfury.gc/storage/general/l=\\r\\nogo.png\\\" alt=3D\\\"MartFury\\\" style=3D\\\"line-height: 100%; outline: none; text-d=\\r\\necoration: none; vertical-align: baseline; font-size: 0; border: 0 none; ma=\\r\\nx-height: 40px;\\\">\\r\\n                                                       =\\r\\n             </a>\\r\\n                                                       =\\r\\n         </td>\\r\\n                                                          =\\r\\n      <td class=3D\\\"bb-text-right\\\" style=3D\\\"font-family: Inter, -apple-syste=\\r\\nm, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, san=\\r\\ns-serif; text-align: right;\\\">\\r\\n                                           =\\r\\n                         2025-08-15 16:38:06\\r\\n                            =\\r\\n                                    </td>\\r\\n                               =\\r\\n                             </tr>\\r\\n                                      =\\r\\n                  </tbody>\\r\\n                                              =\\r\\n      </table>\\r\\n                                                </td>\\r\\n  =\\r\\n                                          </tr>\\r\\n                         =\\r\\n               </tbody>\\r\\n                                    </table>\\r\\n=\\r\\n\\r\\n\\r\\n<div class=3D\\\"bb-main-content\\\">\\r\\n    <table class=3D\\\"bb-box\\\" cellpad=\\r\\nding=3D\\\"0\\\" cellspacing=3D\\\"0\\\" style=3D\\\"font-family: Inter, -apple-system, Bl=\\r\\ninkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-ser=\\r\\nif; border-collapse: collapse; width: 100%; background: #ffffff; border-rad=\\r\\nius: 4px; -webkit-box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05); box-shadow: 0 =\\r\\n1px 4px rgba(0, 0, 0, 0.05); border: 1px solid #dce0e5; -premailer-cellpadd=\\r\\ning: 0; -premailer-cellspacing: 0;\\\">\\r\\n        <tbody>\\r\\n            <tr>=\\r\\n\\r\\n                <td class=3D\\\"bb-content bb-pb-0\\\" align=3D\\\"center\\\" style=\\r\\n=3D\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, S=\\r\\negoe UI, Roboto, Helvetica Neue, sans-serif; padding: 40px 48px; padding-bo=\\r\\nttom: 0;\\\">\\r\\n                    <table class=3D\\\"bb-icon bb-icon-lg bb-bg-b=\\r\\nlue\\\" cellspacing=3D\\\"0\\\" cellpadding=3D\\\"0\\\" style=3D\\\"font-family: Inter, -appl=\\r\\ne-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Ne=\\r\\nue, sans-serif; padding: 0; border-radius: 50%; background: #edeef0; line-h=\\r\\neight: 100%; font-weight: 300; border-collapse: separate; text-align: cente=\\r\\nr; width: 72px; height: 72px; font-size: 48px; background-color: #206bc4; c=\\r\\nolor: #ffffff; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\\\">\\r\\n  =\\r\\n                      <tbody>\\r\\n                            <tr>\\r\\n        =\\r\\n                        <td valign=3D\\\"middle\\\" align=3D\\\"center\\\" style=3D\\\"fon=\\r\\nt-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI=\\r\\n, Roboto, Helvetica Neue, sans-serif;\\\">\\r\\n                                 =\\r\\n   <img src=3D\\\"https://martfury.gc/vendor/core/core/base/images/email-icons=\\r\\n/shopping-cart.png\\\" class=3D\\\"bb-va-middle\\\" width=3D\\\"40\\\" height=3D\\\"40\\\" alt=\\r\\n=3D\\\"Icon\\\" style=3D\\\"border: 0 none; line-height: 100%; outline: none; text-d=\\r\\necoration: none; font-size: 0; vertical-align: middle; display: block; widt=\\r\\nh: 40px; height: 40px;\\\">\\r\\n                                </td>\\r\\n        =\\r\\n                    </tr>\\r\\n                        </tbody>\\r\\n            =\\r\\n        </table>\\r\\n                    <h1 class=3D\\\"bb-text-center bb-m-0 b=\\r\\nb-mt-md\\\" style=3D\\\"font-weight: 600; color: #232b42; font-size: 28px; line-h=\\r\\neight: 130%; text-align: center; margin: 0; margin-top: 16px;\\\">Order succes=\\r\\nsfully!</h1>\\r\\n                </td>\\r\\n            </tr>\\r\\n            <tr>=\\r\\n\\r\\n                <td class=3D\\\"bb-content\\\" style=3D\\\"font-family: Inter, -a=\\r\\npple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica=\\r\\n Neue, sans-serif; padding: 40px 48px;\\\">\\r\\n                    <div>Dear Yo=\\r\\nung Shop,</div>\\r\\n                    <div>You got a new order on MartFury!=\\r\\n</div>\\r\\n                </td>\\r\\n            </tr>\\r\\n            <tr>\\r\\n   =\\r\\n             <td class=3D\\\"bb-content bb-pt-0\\\" style=3D\\\"font-family: Inter, =\\r\\n-apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helveti=\\r\\nca Neue, sans-serif; padding: 40px 48px; padding-top: 0;\\\">\\r\\n              =\\r\\n      <table class=3D\\\"bb-row bb-mb-md\\\" cellpadding=3D\\\"0\\\" cellspacing=3D\\\"0\\\" =\\r\\nstyle=3D\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francis=\\r\\nco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse=\\r\\n; width: 100%; table-layout: fixed; margin-bottom: 16px; -premailer-cellpad=\\r\\nding: 0; -premailer-cellspacing: 0;\\\">\\r\\n                        <tbody>\\r\\n =\\r\\n                           <tr>\\r\\n                                <td class=\\r\\n=3D\\\"bb-bb-col\\\" style=3D\\\"font-family: Inter, -apple-system, BlinkMacSystemFo=\\r\\nnt, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;\\\">\\r\\n      =\\r\\n                              <h4 class=3D\\\"bb-m-0\\\" style=3D\\\"font-weight: 60=\\r\\n0; color: #232b42; font-size: 16px; margin: 0;\\\">Customer Information</h4>=\\r\\n\\r\\n                                    <div>Name: <strong style=3D\\\"font-wei=\\r\\nght: 600;\\\">Fae Koelpin</strong>\\r\\n</div>\\r\\n                                =\\r\\n                                        <div>Phone: <strong style=3D\\\"font-w=\\r\\neight: 600;\\\">+18598017656</strong>\\r\\n</div>\\r\\n                             =\\r\\n                                                                           =\\r\\n                                        <div>Address: <strong style=3D\\\"font=\\r\\n-weight: 600;\\\">2358 Jadon Stream, Bashirianfurt, Utah, PE</strong>\\r\\n</div>=\\r\\n\\r\\n                                                                    </td=\\r\\n>\\r\\n                            </tr>\\r\\n                        </tbody>\\r\\n=\\r\\n                    </table>\\r\\n                </td>\\r\\n            </tr>\\r\\n=\\r\\n            <tr>\\r\\n                <td class=3D\\\"bb-content bb-pt-0\\\" style=\\r\\n=3D\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, S=\\r\\negoe UI, Roboto, Helvetica Neue, sans-serif; padding: 40px 48px; padding-to=\\r\\np: 0;\\\">\\r\\n                    <h4 style=3D\\\"font-weight: 600; margin: 0 0 0.=\\r\\n5em; color: #232b42; font-size: 16px;\\\">Here\\'s what you ordered:</h4>\\r\\n    =\\r\\n                <a class=3D\\\"button button-blue\\\" href=3D\\\"https://martfury.gc=\\r\\n/orders/tracking?order_id=3D%2310000052&amp;email=3Dvendor%40botble.com\\\" st=\\r\\nyle=3D\\\"color: #206bc4; text-decoration: none;\\\">View order</a>\\r\\n    or <a h=\\r\\nref=3D\\\"https://martfury.gc\\\" style=3D\\\"color: #206bc4; text-decoration: none;=\\r\\n\\\">Go to our shop</a>\\r\\n\\r\\n    <br>\\r\\n\\r\\n<table class=3D\\\"bb-table\\\" cellspaci=\\r\\nng=3D\\\"0\\\" cellpadding=3D\\\"0\\\" style=3D\\\"font-family: Inter, -apple-system, Blin=\\r\\nkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif=\\r\\n; border-collapse: collapse; width: 100%; -premailer-cellpadding: 0; -prema=\\r\\niler-cellspacing: 0;\\\">\\r\\n    <thead>\\r\\n        <tr>\\r\\n            <th colsp=\\r\\nan=3D\\\"2\\\" style=3D\\\"text-transform: uppercase; font-weight: 600; color: #6673=\\r\\n82; font-size: 12px; padding: 0 0 4px 0; padding-left: 0;\\\"></th>\\r\\n        =\\r\\n    <th style=3D\\\"text-transform: uppercase; font-weight: 600; color: #66738=\\r\\n2; font-size: 12px; padding: 0 0 4px 0;\\\">Quantity</th>\\r\\n            <th cl=\\r\\nass=3D\\\"bb-text-right\\\" style=3D\\\"text-align: right; text-transform: uppercase=\\r\\n; font-weight: 600; color: #667382; font-size: 12px; padding: 0 0 4px 0; pa=\\r\\ndding-right: 0;\\\">Price</th>\\r\\n        </tr>\\r\\n    </thead>\\r\\n\\r\\n    <tbody>=\\r\\n\\r\\n                <tr>\\r\\n            <td class=3D\\\"bb-pr-0\\\" style=3D\\\"font-f=\\r\\namily: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, R=\\r\\noboto, Helvetica Neue, sans-serif; padding-right: 0; padding: 4px 0; paddin=\\r\\ng-left: 0;\\\">\\r\\n                <a href=3D\\\"\\\" style=3D\\\"color: #206bc4; text-d=\\r\\necoration: none;\\\">\\r\\n                    <img src=3D\\\"https://martfury.gc/st=\\r\\norage/products/6-150x150.jpg\\\" class=3D\\\" bb-rounded\\\" width=3D\\\"64\\\" height=3D\\\"=\\r\\n64\\\" alt=3D\\\"\\\" style=3D\\\"line-height: 100%; outline: none; text-decoration: no=\\r\\nne; vertical-align: baseline; font-size: 0; border: 0 none; border-radius: =\\r\\n4px;\\\">\\r\\n                </a>\\r\\n            </td>\\r\\n            <td class=\\r\\n=3D\\\"bb-pl-md bb-w-100p\\\" style=3D\\\"font-family: Inter, -apple-system, BlinkMa=\\r\\ncSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; w=\\r\\nidth: 100%; padding-left: 16px !important; padding: 4px 0;\\\">\\r\\n            =\\r\\n    <strong style=3D\\\"font-weight: 600;\\\">Nikon HD camera</strong><br>\\r\\n    =\\r\\n                                <span class=3D\\\"bb-text-muted\\\" style=3D\\\"colo=\\r\\nr: #667382;\\\">(Color: Black, Size: XXL)</span>\\r\\n               =20\\r\\n        =\\r\\n                    </td>\\r\\n            <td class=3D\\\"bb-text-center\\\" style=\\r\\n=3D\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, S=\\r\\negoe UI, Roboto, Helvetica Neue, sans-serif; text-align: center; padding: 4=\\r\\npx 0;\\\">x 1</td>\\r\\n            <td class=3D\\\"bb-text-right\\\" style=3D\\\"font-fam=\\r\\nily: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Rob=\\r\\noto, Helvetica Neue, sans-serif; text-align: right; padding: 4px 0; padding=\\r\\n-right: 0;\\\">SAR480.00</td>\\r\\n        </tr>\\r\\n   =20\\r\\n                       =\\r\\n             <tr>\\r\\n                    <td colspan=3D\\\"2\\\" class=3D\\\"bb-borde=\\r\\nr-top bb-text-right\\\" style=3D\\\"font-family: Inter, -apple-system, BlinkMacSy=\\r\\nstemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text=\\r\\n-align: right; border-top: 1px solid #dce0e5; padding: 4px 0; padding-left:=\\r\\n 0;\\\">Subtotal</td>\\r\\n                    <td colspan=3D\\\"2\\\" class=3D\\\"bb-bord=\\r\\ner-top bb-text-right\\\" style=3D\\\"font-family: Inter, -apple-system, BlinkMacS=\\r\\nystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; tex=\\r\\nt-align: right; border-top: 1px solid #dce0e5; padding: 4px 0; padding-righ=\\r\\nt: 0;\\\">SAR480.00</td>\\r\\n                </tr>\\r\\n           =20\\r\\n            =\\r\\n\\r\\n                            <tr>\\r\\n                    <td colspan=3D\\\"2\\\"=\\r\\n class=3D\\\"bb-text-right\\\" style=3D\\\"font-family: Inter, -apple-system, BlinkM=\\r\\nacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; =\\r\\ntext-align: right; padding: 4px 0; padding-left: 0;\\\">Tax</td>\\r\\n           =\\r\\n         <td colspan=3D\\\"2\\\" class=3D\\\"bb-text-right\\\" style=3D\\\"font-family: In=\\r\\nter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, He=\\r\\nlvetica Neue, sans-serif; text-align: right; padding: 4px 0; padding-right:=\\r\\n 0;\\\">SAR48.00</td>\\r\\n                </tr>\\r\\n           =20\\r\\n           =20\\r\\n=\\r\\n                        <tr>\\r\\n                <td colspan=3D\\\"2\\\" class=3D\\\"b=\\r\\nb-text-right bb-font-strong bb-h3 bb-m-0\\\" style=3D\\\"font-family: Inter, -app=\\r\\nle-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica N=\\r\\neue, sans-serif; color: #232b42; font-size: 20px; line-height: 130%; text-a=\\r\\nlign: right; font-weight: 600; margin: 0; padding: 4px 0; padding-left: 0;\\\"=\\r\\n>Total</td>\\r\\n                <td colspan=3D\\\"2\\\" class=3D\\\"bb-font-strong bb-=\\r\\nh3 bb-m-0 bb-text-right\\\" style=3D\\\"font-family: Inter, -apple-system, BlinkM=\\r\\nacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; =\\r\\ncolor: #232b42; font-size: 20px; line-height: 130%; text-align: right; font=\\r\\n-weight: 600; margin: 0; padding: 4px 0; padding-right: 0;\\\">SAR528.00</td>=\\r\\n\\r\\n            </tr>\\r\\n            </tbody>\\r\\n</table>\\r\\n\\r\\n\\r\\n            =\\r\\n                        </td>\\r\\n            </tr>\\r\\n            <tr>\\r\\n    =\\r\\n            <td class=3D\\\"bb-content bb-border-top\\\" style=3D\\\"font-family: In=\\r\\nter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, He=\\r\\nlvetica Neue, sans-serif; padding: 40px 48px; border-top: 1px solid #dce0e5=\\r\\n;\\\">\\r\\n                    <table class=3D\\\"bb-row bb-mb-md\\\" cellpadding=3D\\\"0=\\r\\n\\\" cellspacing=3D\\\"0\\\" style=3D\\\"font-family: Inter, -apple-system, BlinkMacSys=\\r\\ntemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; borde=\\r\\nr-collapse: collapse; width: 100%; table-layout: fixed; margin-bottom: 16px=\\r\\n; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\\\">\\r\\n               =\\r\\n         <tbody>\\r\\n                            <tr>\\r\\n                     =\\r\\n           <td class=3D\\\"bb-bb-col\\\" style=3D\\\"font-family: Inter, -apple-syst=\\r\\nem, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sa=\\r\\nns-serif;\\\">\\r\\n                                    <h4 class=3D\\\"bb-m-0\\\" styl=\\r\\ne=3D\\\"font-weight: 600; color: #232b42; font-size: 16px; margin: 0;\\\">Order n=\\r\\number</h4>\\r\\n                                    <div>#10000052</div>\\r\\n   =\\r\\n                             </td>\\r\\n                                <td cl=\\r\\nass=3D\\\"bb-col-spacer\\\" style=3D\\\"font-family: Inter, -apple-system, BlinkMacS=\\r\\nystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; ver=\\r\\ntical-align: top; width: 24px;\\\"></td>\\r\\n                                <td=\\r\\n class=3D\\\"bb-col\\\" style=3D\\\"font-family: Inter, -apple-system, BlinkMacSyste=\\r\\nmFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; vertica=\\r\\nl-align: top;\\\">\\r\\n                                    <h4 class=3D\\\"bb-mb-0\\\"=\\r\\n style=3D\\\"font-weight: 600; margin: 0 0 0.5em; color: #232b42; font-size: 1=\\r\\n6px; margin-bottom: 0;\\\">Order date</h4>\\r\\n                                 =\\r\\n   <div>2025-08-15 16:37:22</div>\\r\\n                                </td>=\\r\\n\\r\\n                            </tr>\\r\\n                        </tbody>\\r\\n =\\r\\n                   </table>\\r\\n                    <table class=3D\\\"bb-row\\\" c=\\r\\nellpadding=3D\\\"0\\\" cellspacing=3D\\\"0\\\" style=3D\\\"font-family: Inter, -apple-syst=\\r\\nem, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sa=\\r\\nns-serif; border-collapse: collapse; width: 100%; table-layout: fixed; -pre=\\r\\nmailer-cellpadding: 0; -premailer-cellspacing: 0;\\\">\\r\\n                     =\\r\\n   <tbody>\\r\\n                            <tr>\\r\\n                           =\\r\\n     <td class=3D\\\"bb-col\\\" style=3D\\\"font-family: Inter, -apple-system, Blink=\\r\\nMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;=\\r\\n vertical-align: top;\\\">\\r\\n                                                 =\\r\\n                       <h4 class=3D\\\"bb-m-0\\\" style=3D\\\"font-weight: 600; colo=\\r\\nr: #232b42; font-size: 16px; margin: 0;\\\">Shipping Method</h4>\\r\\n           =\\r\\n                         <div>\\r\\n                                        Lo=\\r\\ncal Pickup\\r\\n                                    </div>\\r\\n                 =\\r\\n                                                   </td>\\r\\n\\r\\n             =\\r\\n                   <td class=3D\\\"bb-col-spacer\\\" style=3D\\\"font-family: Inter,=\\r\\n -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvet=\\r\\nica Neue, sans-serif; vertical-align: top; width: 24px;\\\"></td>\\r\\n          =\\r\\n                      <td class=3D\\\"bb-col\\\" style=3D\\\"font-family: Inter, -ap=\\r\\nple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica =\\r\\nNeue, sans-serif; vertical-align: top;\\\">\\r\\n                                =\\r\\n    <h4 class=3D\\\"bb-m-0\\\" style=3D\\\"font-weight: 600; color: #232b42; font-si=\\r\\nze: 16px; margin: 0;\\\">Payment Method</h4>\\r\\n                               =\\r\\n     <div>\\r\\n                                        HyperPay\\r\\n           =\\r\\n                         </div>\\r\\n                                </td>\\r\\n =\\r\\n                           </tr>\\r\\n                        </tbody>\\r\\n     =\\r\\n               </table>\\r\\n                </td>\\r\\n            </tr>\\r\\n     =\\r\\n   </tbody>\\r\\n    </table>\\r\\n</div>\\r\\n\\r\\n<table cellspacing=3D\\\"0\\\" cellpaddi=\\r\\nng=3D\\\"0\\\" style=3D\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, Sa=\\r\\nn Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse:=\\r\\n collapse; width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: =\\r\\n0;\\\">\\r\\n    <tbody>\\r\\n        <tr>\\r\\n            <td class=3D\\\"bb-py-xl\\\" styl=\\r\\ne=3D\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, =\\r\\nSegoe UI, Roboto, Helvetica Neue, sans-serif; padding-top: 48px; padding-bo=\\r\\nttom: 48px;\\\">\\r\\n                <table class=3D\\\"bb-text-center bb-text-mute=\\r\\nd\\\" cellspacing=3D\\\"0\\\" cellpadding=3D\\\"0\\\" style=3D\\\"font-family: Inter, -apple-=\\r\\nsystem, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue=\\r\\n, sans-serif; border-collapse: collapse; width: 100%; color: #667382; text-=\\r\\nalign: center; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\\\">\\r\\n  =\\r\\n                  <tbody>\\r\\n                   =20\\r\\n                    <tr>=\\r\\n\\r\\n                        <td class=3D\\\"bb-px-lg\\\" style=3D\\\"font-family: Int=\\r\\ner, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Hel=\\r\\nvetica Neue, sans-serif; padding-right: 24px; padding-left: 24px;\\\">\\r\\n     =\\r\\n                       =C2=A9 2025 MartFury. All Rights Reserved.\\r\\n       =\\r\\n                 </td>\\r\\n                    </tr>\\r\\n\\r\\n                   =\\r\\n                         <tr>\\r\\n                            <td class=3D\\\"bb=\\r\\n-pt-md\\\" style=3D\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San=\\r\\n Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding-top: 16px=\\r\\n;\\\">\\r\\n                                If you have any questions, feel free =\\r\\nto message us at <a href=3D\\\"mailto:<EMAIL>\\\" style=3D\\\"color: #206bc=\\r\\n4; text-decoration: none;\\\"><EMAIL></a>.\\r\\n                        =\\r\\n    </td>\\r\\n                        </tr>\\r\\n                               =\\r\\n         </tbody>\\r\\n                </table>\\r\\n            </td>\\r\\n        =\\r\\n</tr>\\r\\n    </tbody>\\r\\n</table>\\r\\n</td>\\r\\n</tr>\\r\\n</tbody>\\r\\n</table>\\r\\n</t=\\r\\nd>\\r\\n</tr>\\r\\n</tbody>\\r\\n</table>\\r\\n</center>\\r\\n</body>\\r\\n\\r\\n</html>', '', '2025-08-15 16:38:06', '2025-08-15 16:38:06')", "type": "query", "params": [], "bindings": ["\"Example\" <<EMAIL>>", "<EMAIL>", "", "", "New order(s) at MartFury", "<!doctype html>\n<html lang=\"en\">\n\n<head>\n    <meta charset=\"UTF-8\">\n    <title>MartFury</title>\n</head>\n\n<body class=\"bb-bg-body\" dir=\"ltr\" style=\"margin: 0; padding: 0; font-size: 14px; line-height: 171.4285714286%; mso-line-height-rule: exactly; color: #3A4859; width: 100%; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale; -webkit-font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11'; font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11'; font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; background-color: #f6f7f9;\">\n\n<center>\n    <table class=\"bb-main bb-bg-body\" width=\"100%\" cellspacing=\"0\" cellpadding=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; background-color: #f6f7f9;\">\n        <tbody>\n            <tr>\n                <td align=\"center\" valign=\"top\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;\">\n                    <table class=\"bb-wrap\" cellspacing=\"0\" cellpadding=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; max-width: 640px; text-align: left; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\n                        <tbody>\n                            <tr>\n                                <td class=\"bb-p-sm\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 8px;\">\n                                    <table cellpadding=\"0\" cellspacing=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\n                                        <tbody>\n                                            <tr>\n                                                <td class=\"bb-py-lg\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding-top: 24px; padding-bottom: 24px;\">\n                                                    <table cellspacing=\"0\" cellpadding=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\n                                                        <tbody>\n                                                            <tr>\n                                                                <td class=\"bb-text-left\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: left;\">\n                                                                    <a href=\"https://martfury.gc\" style=\"color: #206bc4; text-decoration: none;\">\n                                                                        <img class=\"bb-logo\" src=\"https://martfury.gc/storage/general/logo.png\" alt=\"MartFury\" style=\"line-height: 100%; outline: none; text-decoration: none; vertical-align: baseline; font-size: 0; border: 0 none; max-height: 40px;\">\n                                                                    </a>\n                                                                </td>\n                                                                <td class=\"bb-text-right\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right;\">\n                                                                    2025-08-15 16:38:06\n                                                                </td>\n                                                            </tr>\n                                                        </tbody>\n                                                    </table>\n                                                </td>\n                                            </tr>\n                                        </tbody>\n                                    </table>\n\n\n<div class=\"bb-main-content\">\n    <table class=\"bb-box\" cellpadding=\"0\" cellspacing=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; background: #ffffff; border-radius: 4px; -webkit-box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05); box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05); border: 1px solid #dce0e5; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\n        <tbody>\n            <tr>\n                <td class=\"bb-content bb-pb-0\" align=\"center\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 40px 48px; padding-bottom: 0;\">\n                    <table class=\"bb-icon bb-icon-lg bb-bg-blue\" cellspacing=\"0\" cellpadding=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 0; border-radius: 50%; background: #edeef0; line-height: 100%; font-weight: 300; border-collapse: separate; text-align: center; width: 72px; height: 72px; font-size: 48px; background-color: #206bc4; color: #ffffff; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\n                        <tbody>\n                            <tr>\n                                <td valign=\"middle\" align=\"center\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;\">\n                                    <img src=\"https://martfury.gc/vendor/core/core/base/images/email-icons/shopping-cart.png\" class=\"bb-va-middle\" width=\"40\" height=\"40\" alt=\"Icon\" style=\"border: 0 none; line-height: 100%; outline: none; text-decoration: none; font-size: 0; vertical-align: middle; display: block; width: 40px; height: 40px;\">\n                                </td>\n                            </tr>\n                        </tbody>\n                    </table>\n                    <h1 class=\"bb-text-center bb-m-0 bb-mt-md\" style=\"font-weight: 600; color: #232b42; font-size: 28px; line-height: 130%; text-align: center; margin: 0; margin-top: 16px;\">Order successfully!</h1>\n                </td>\n            </tr>\n            <tr>\n                <td class=\"bb-content\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 40px 48px;\">\n                    <div>Dear Young Shop,</div>\n                    <div>You got a new order on MartFury!</div>\n                </td>\n            </tr>\n            <tr>\n                <td class=\"bb-content bb-pt-0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 40px 48px; padding-top: 0;\">\n                    <table class=\"bb-row bb-mb-md\" cellpadding=\"0\" cellspacing=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; table-layout: fixed; margin-bottom: 16px; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\n                        <tbody>\n                            <tr>\n                                <td class=\"bb-bb-col\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;\">\n                                    <h4 class=\"bb-m-0\" style=\"font-weight: 600; color: #232b42; font-size: 16px; margin: 0;\">Customer Information</h4>\n                                    <div>Name: <strong style=\"font-weight: 600;\">Fae Koelpin</strong>\n</div>\n                                                                        <div>Phone: <strong style=\"font-weight: 600;\">+18598017656</strong>\n</div>\n                                                                                                                                                <div>Address: <strong style=\"font-weight: 600;\">2358 Jadon Stream, Bashirianfurt, Utah, PE</strong>\n</div>\n                                                                    </td>\n                            </tr>\n                        </tbody>\n                    </table>\n                </td>\n            </tr>\n            <tr>\n                <td class=\"bb-content bb-pt-0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 40px 48px; padding-top: 0;\">\n                    <h4 style=\"font-weight: 600; margin: 0 0 0.5em; color: #232b42; font-size: 16px;\">Here's what you ordered:</h4>\n                    <a class=\"button button-blue\" href=\"https://martfury.gc/orders/tracking?order_id=%2310000052&amp;email=vendor%40botble.com\" style=\"color: #206bc4; text-decoration: none;\">View order</a>\n    or <a href=\"https://martfury.gc\" style=\"color: #206bc4; text-decoration: none;\">Go to our shop</a>\n\n    <br>\n\n<table class=\"bb-table\" cellspacing=\"0\" cellpadding=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\n    <thead>\n        <tr>\n            <th colspan=\"2\" style=\"text-transform: uppercase; font-weight: 600; color: #667382; font-size: 12px; padding: 0 0 4px 0; padding-left: 0;\"></th>\n            <th style=\"text-transform: uppercase; font-weight: 600; color: #667382; font-size: 12px; padding: 0 0 4px 0;\">Quantity</th>\n            <th class=\"bb-text-right\" style=\"text-align: right; text-transform: uppercase; font-weight: 600; color: #667382; font-size: 12px; padding: 0 0 4px 0; padding-right: 0;\">Price</th>\n        </tr>\n    </thead>\n\n    <tbody>\n                <tr>\n            <td class=\"bb-pr-0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding-right: 0; padding: 4px 0; padding-left: 0;\">\n                <a href=\"\" style=\"color: #206bc4; text-decoration: none;\">\n                    <img src=\"https://martfury.gc/storage/products/6-150x150.jpg\" class=\" bb-rounded\" width=\"64\" height=\"64\" alt=\"\" style=\"line-height: 100%; outline: none; text-decoration: none; vertical-align: baseline; font-size: 0; border: 0 none; border-radius: 4px;\">\n                </a>\n            </td>\n            <td class=\"bb-pl-md bb-w-100p\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; width: 100%; padding-left: 16px !important; padding: 4px 0;\">\n                <strong style=\"font-weight: 600;\">Nikon HD camera</strong><br>\n                                    <span class=\"bb-text-muted\" style=\"color: #667382;\">(Color: Black, Size: XXL)</span>\n                \n                            </td>\n            <td class=\"bb-text-center\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: center; padding: 4px 0;\">x 1</td>\n            <td class=\"bb-text-right\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right; padding: 4px 0; padding-right: 0;\">SAR480.00</td>\n        </tr>\n    \n                                    <tr>\n                    <td colspan=\"2\" class=\"bb-border-top bb-text-right\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right; border-top: 1px solid #dce0e5; padding: 4px 0; padding-left: 0;\">Subtotal</td>\n                    <td colspan=\"2\" class=\"bb-border-top bb-text-right\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right; border-top: 1px solid #dce0e5; padding: 4px 0; padding-right: 0;\">SAR480.00</td>\n                </tr>\n            \n            \n                            <tr>\n                    <td colspan=\"2\" class=\"bb-text-right\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right; padding: 4px 0; padding-left: 0;\">Tax</td>\n                    <td colspan=\"2\" class=\"bb-text-right\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right; padding: 4px 0; padding-right: 0;\">SAR48.00</td>\n                </tr>\n            \n            \n                        <tr>\n                <td colspan=\"2\" class=\"bb-text-right bb-font-strong bb-h3 bb-m-0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; color: #232b42; font-size: 20px; line-height: 130%; text-align: right; font-weight: 600; margin: 0; padding: 4px 0; padding-left: 0;\">Total</td>\n                <td colspan=\"2\" class=\"bb-font-strong bb-h3 bb-m-0 bb-text-right\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; color: #232b42; font-size: 20px; line-height: 130%; text-align: right; font-weight: 600; margin: 0; padding: 4px 0; padding-right: 0;\">SAR528.00</td>\n            </tr>\n            </tbody>\n</table>\n\n\n                                    </td>\n            </tr>\n            <tr>\n                <td class=\"bb-content bb-border-top\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 40px 48px; border-top: 1px solid #dce0e5;\">\n                    <table class=\"bb-row bb-mb-md\" cellpadding=\"0\" cellspacing=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; table-layout: fixed; margin-bottom: 16px; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\n                        <tbody>\n                            <tr>\n                                <td class=\"bb-bb-col\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;\">\n                                    <h4 class=\"bb-m-0\" style=\"font-weight: 600; color: #232b42; font-size: 16px; margin: 0;\">Order number</h4>\n                                    <div>#10000052</div>\n                                </td>\n                                <td class=\"bb-col-spacer\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; vertical-align: top; width: 24px;\"></td>\n                                <td class=\"bb-col\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; vertical-align: top;\">\n                                    <h4 class=\"bb-mb-0\" style=\"font-weight: 600; margin: 0 0 0.5em; color: #232b42; font-size: 16px; margin-bottom: 0;\">Order date</h4>\n                                    <div>2025-08-15 16:37:22</div>\n                                </td>\n                            </tr>\n                        </tbody>\n                    </table>\n                    <table class=\"bb-row\" cellpadding=\"0\" cellspacing=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; table-layout: fixed; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\n                        <tbody>\n                            <tr>\n                                <td class=\"bb-col\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; vertical-align: top;\">\n                                                                        <h4 class=\"bb-m-0\" style=\"font-weight: 600; color: #232b42; font-size: 16px; margin: 0;\">Shipping Method</h4>\n                                    <div>\n                                        Local Pickup\n                                    </div>\n                                                                    </td>\n\n                                <td class=\"bb-col-spacer\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; vertical-align: top; width: 24px;\"></td>\n                                <td class=\"bb-col\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; vertical-align: top;\">\n                                    <h4 class=\"bb-m-0\" style=\"font-weight: 600; color: #232b42; font-size: 16px; margin: 0;\">Payment Method</h4>\n                                    <div>\n                                        HyperPay\n                                    </div>\n                                </td>\n                            </tr>\n                        </tbody>\n                    </table>\n                </td>\n            </tr>\n        </tbody>\n    </table>\n</div>\n\n<table cellspacing=\"0\" cellpadding=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\n    <tbody>\n        <tr>\n            <td class=\"bb-py-xl\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding-top: 48px; padding-bottom: 48px;\">\n                <table class=\"bb-text-center bb-text-muted\" cellspacing=\"0\" cellpadding=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; color: #667382; text-align: center; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\n                    <tbody>\n                    \n                    <tr>\n                        <td class=\"bb-px-lg\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding-right: 24px; padding-left: 24px;\">\n                            © 2025 MartFury. All Rights Reserved.\n                        </td>\n                    </tr>\n\n                                            <tr>\n                            <td class=\"bb-pt-md\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding-top: 16px;\">\n                                If you have any questions, feel free to message us at <a href=\"mailto:<EMAIL>\" style=\"color: #206bc4; text-decoration: none;\"><EMAIL></a>.\n                            </td>\n                        </tr>\n                                        </tbody>\n                </table>\n            </td>\n        </tr>\n    </tbody>\n</table>\n</td>\n</tr>\n</tbody>\n</table>\n</td>\n</tr>\n</tbody>\n</table>\n</center>\n</body>\n\n</html>", null, "From: Example <<EMAIL>>\r\nTo: kamron.k<PERSON><PERSON><PERSON>@example.org\r\nSubject: New order(s) at MartFury\r\nMessage-ID: <<EMAIL>>\r\nMIME-Version: 1.0\r\nDate: Fri, 15 Aug 2025 16:38:06 +0000\r\nContent-Type: text/html; charset=utf-8\r\nContent-Transfer-Encoding: quoted-printable\r\n\r\n<!doctype html>\r\n<html lang=3D\"en\">\r\n\r\n<head>\r\n    <meta charset=3D\"UTF=\r\n-8\">\r\n    <title>MartFury</title>\r\n</head>\r\n\r\n<body class=3D\"bb-bg-body=\r\n\" dir=3D\"ltr\" style=3D\"margin: 0; padding: 0; font-size: 14px; line-height:=\r\n 171.4285714286%; mso-line-height-rule: exactly; color: #3A4859; width: 100=\r\n%; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale;=\r\n -webkit-font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11'; font-featur=\r\ne-settings: 'cv02', 'cv03', 'cv04', 'cv11'; font-family: Inter, -apple-syst=\r\nem, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sa=\r\nns-serif; background-color: #f6f7f9;\">\r\n\r\n<center>\r\n    <table class=3D\"=\r\nbb-main bb-bg-body\" width=3D\"100%\" cellspacing=3D\"0\" cellpadding=3D\"0\" styl=\r\ne=3D\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, =\r\nSegoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; wi=\r\ndth: 100%; -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; back=\r\nground-color: #f6f7f9;\">\r\n        <tbody>\r\n            <tr>\r\n           =\r\n     <td align=3D\"center\" valign=3D\"top\" style=3D\"font-family: Inter, -appl=\r\ne-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Ne=\r\nue, sans-serif;\">\r\n                    <table class=3D\"bb-wrap\" cellspacin=\r\ng=3D\"0\" cellpadding=3D\"0\" style=3D\"font-family: Inter, -apple-system, Blink=\r\nMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;=\r\n border-collapse: collapse; width: 100%; max-width: 640px; text-align: left=\r\n; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\r\n               =\r\n         <tbody>\r\n                            <tr>\r\n                     =\r\n           <td class=3D\"bb-p-sm\" style=3D\"font-family: Inter, -apple-system=\r\n, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans=\r\n-serif; padding: 8px;\">\r\n                                    <table cellpa=\r\ndding=3D\"0\" cellspacing=3D\"0\" style=3D\"font-family: Inter, -apple-system, B=\r\nlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-se=\r\nrif; border-collapse: collapse; width: 100%; -premailer-cellpadding: 0; -pr=\r\nemailer-cellspacing: 0;\">\r\n                                        <tbody>=\r\n\r\n                                            <tr>\r\n                     =\r\n                           <td class=3D\"bb-py-lg\" style=3D\"font-family: Int=\r\ner, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Hel=\r\nvetica Neue, sans-serif; padding-top: 24px; padding-bottom: 24px;\">\r\n     =\r\n                                               <table cellspacing=3D\"0\" cel=\r\nlpadding=3D\"0\" style=3D\"font-family: Inter, -apple-system, BlinkMacSystemFo=\r\nnt, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-col=\r\nlapse: collapse; width: 100%; -premailer-cellpadding: 0; -premailer-cellspa=\r\ncing: 0;\">\r\n                                                        <tbody=\r\n>\r\n                                                            <tr>\r\n    =\r\n                                                            <td class=3D\"bb=\r\n-text-left\" style=3D\"font-family: Inter, -apple-system, BlinkMacSystemFont,=\r\n San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: l=\r\neft;\">\r\n                                                                  =\r\n  <a href=3D\"https://martfury.gc\" style=3D\"color: #206bc4; text-decoration:=\r\n none;\">\r\n                                                                =\r\n        <img class=3D\"bb-logo\" src=3D\"https://martfury.gc/storage/general/l=\r\nogo.png\" alt=3D\"MartFury\" style=3D\"line-height: 100%; outline: none; text-d=\r\necoration: none; vertical-align: baseline; font-size: 0; border: 0 none; ma=\r\nx-height: 40px;\">\r\n                                                       =\r\n             </a>\r\n                                                       =\r\n         </td>\r\n                                                          =\r\n      <td class=3D\"bb-text-right\" style=3D\"font-family: Inter, -apple-syste=\r\nm, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, san=\r\ns-serif; text-align: right;\">\r\n                                           =\r\n                         2025-08-15 16:38:06\r\n                            =\r\n                                    </td>\r\n                               =\r\n                             </tr>\r\n                                      =\r\n                  </tbody>\r\n                                              =\r\n      </table>\r\n                                                </td>\r\n  =\r\n                                          </tr>\r\n                         =\r\n               </tbody>\r\n                                    </table>\r\n=\r\n\r\n\r\n<div class=3D\"bb-main-content\">\r\n    <table class=3D\"bb-box\" cellpad=\r\nding=3D\"0\" cellspacing=3D\"0\" style=3D\"font-family: Inter, -apple-system, Bl=\r\ninkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-ser=\r\nif; border-collapse: collapse; width: 100%; background: #ffffff; border-rad=\r\nius: 4px; -webkit-box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05); box-shadow: 0 =\r\n1px 4px rgba(0, 0, 0, 0.05); border: 1px solid #dce0e5; -premailer-cellpadd=\r\ning: 0; -premailer-cellspacing: 0;\">\r\n        <tbody>\r\n            <tr>=\r\n\r\n                <td class=3D\"bb-content bb-pb-0\" align=3D\"center\" style=\r\n=3D\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, S=\r\negoe UI, Roboto, Helvetica Neue, sans-serif; padding: 40px 48px; padding-bo=\r\nttom: 0;\">\r\n                    <table class=3D\"bb-icon bb-icon-lg bb-bg-b=\r\nlue\" cellspacing=3D\"0\" cellpadding=3D\"0\" style=3D\"font-family: Inter, -appl=\r\ne-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Ne=\r\nue, sans-serif; padding: 0; border-radius: 50%; background: #edeef0; line-h=\r\neight: 100%; font-weight: 300; border-collapse: separate; text-align: cente=\r\nr; width: 72px; height: 72px; font-size: 48px; background-color: #206bc4; c=\r\nolor: #ffffff; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\r\n  =\r\n                      <tbody>\r\n                            <tr>\r\n        =\r\n                        <td valign=3D\"middle\" align=3D\"center\" style=3D\"fon=\r\nt-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI=\r\n, Roboto, Helvetica Neue, sans-serif;\">\r\n                                 =\r\n   <img src=3D\"https://martfury.gc/vendor/core/core/base/images/email-icons=\r\n/shopping-cart.png\" class=3D\"bb-va-middle\" width=3D\"40\" height=3D\"40\" alt=\r\n=3D\"Icon\" style=3D\"border: 0 none; line-height: 100%; outline: none; text-d=\r\necoration: none; font-size: 0; vertical-align: middle; display: block; widt=\r\nh: 40px; height: 40px;\">\r\n                                </td>\r\n        =\r\n                    </tr>\r\n                        </tbody>\r\n            =\r\n        </table>\r\n                    <h1 class=3D\"bb-text-center bb-m-0 b=\r\nb-mt-md\" style=3D\"font-weight: 600; color: #232b42; font-size: 28px; line-h=\r\neight: 130%; text-align: center; margin: 0; margin-top: 16px;\">Order succes=\r\nsfully!</h1>\r\n                </td>\r\n            </tr>\r\n            <tr>=\r\n\r\n                <td class=3D\"bb-content\" style=3D\"font-family: Inter, -a=\r\npple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica=\r\n Neue, sans-serif; padding: 40px 48px;\">\r\n                    <div>Dear Yo=\r\nung Shop,</div>\r\n                    <div>You got a new order on MartFury!=\r\n</div>\r\n                </td>\r\n            </tr>\r\n            <tr>\r\n   =\r\n             <td class=3D\"bb-content bb-pt-0\" style=3D\"font-family: Inter, =\r\n-apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helveti=\r\nca Neue, sans-serif; padding: 40px 48px; padding-top: 0;\">\r\n              =\r\n      <table class=3D\"bb-row bb-mb-md\" cellpadding=3D\"0\" cellspacing=3D\"0\" =\r\nstyle=3D\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francis=\r\nco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse=\r\n; width: 100%; table-layout: fixed; margin-bottom: 16px; -premailer-cellpad=\r\nding: 0; -premailer-cellspacing: 0;\">\r\n                        <tbody>\r\n =\r\n                           <tr>\r\n                                <td class=\r\n=3D\"bb-bb-col\" style=3D\"font-family: Inter, -apple-system, BlinkMacSystemFo=\r\nnt, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;\">\r\n      =\r\n                              <h4 class=3D\"bb-m-0\" style=3D\"font-weight: 60=\r\n0; color: #232b42; font-size: 16px; margin: 0;\">Customer Information</h4>=\r\n\r\n                                    <div>Name: <strong style=3D\"font-wei=\r\nght: 600;\">Fae Koelpin</strong>\r\n</div>\r\n                                =\r\n                                        <div>Phone: <strong style=3D\"font-w=\r\neight: 600;\">+18598017656</strong>\r\n</div>\r\n                             =\r\n                                                                           =\r\n                                        <div>Address: <strong style=3D\"font=\r\n-weight: 600;\">2358 Jadon Stream, Bashirianfurt, Utah, PE</strong>\r\n</div>=\r\n\r\n                                                                    </td=\r\n>\r\n                            </tr>\r\n                        </tbody>\r\n=\r\n                    </table>\r\n                </td>\r\n            </tr>\r\n=\r\n            <tr>\r\n                <td class=3D\"bb-content bb-pt-0\" style=\r\n=3D\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, S=\r\negoe UI, Roboto, Helvetica Neue, sans-serif; padding: 40px 48px; padding-to=\r\np: 0;\">\r\n                    <h4 style=3D\"font-weight: 600; margin: 0 0 0.=\r\n5em; color: #232b42; font-size: 16px;\">Here's what you ordered:</h4>\r\n    =\r\n                <a class=3D\"button button-blue\" href=3D\"https://martfury.gc=\r\n/orders/tracking?order_id=3D%2310000052&amp;email=3Dvendor%40botble.com\" st=\r\nyle=3D\"color: #206bc4; text-decoration: none;\">View order</a>\r\n    or <a h=\r\nref=3D\"https://martfury.gc\" style=3D\"color: #206bc4; text-decoration: none;=\r\n\">Go to our shop</a>\r\n\r\n    <br>\r\n\r\n<table class=3D\"bb-table\" cellspaci=\r\nng=3D\"0\" cellpadding=3D\"0\" style=3D\"font-family: Inter, -apple-system, Blin=\r\nkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif=\r\n; border-collapse: collapse; width: 100%; -premailer-cellpadding: 0; -prema=\r\niler-cellspacing: 0;\">\r\n    <thead>\r\n        <tr>\r\n            <th colsp=\r\nan=3D\"2\" style=3D\"text-transform: uppercase; font-weight: 600; color: #6673=\r\n82; font-size: 12px; padding: 0 0 4px 0; padding-left: 0;\"></th>\r\n        =\r\n    <th style=3D\"text-transform: uppercase; font-weight: 600; color: #66738=\r\n2; font-size: 12px; padding: 0 0 4px 0;\">Quantity</th>\r\n            <th cl=\r\nass=3D\"bb-text-right\" style=3D\"text-align: right; text-transform: uppercase=\r\n; font-weight: 600; color: #667382; font-size: 12px; padding: 0 0 4px 0; pa=\r\ndding-right: 0;\">Price</th>\r\n        </tr>\r\n    </thead>\r\n\r\n    <tbody>=\r\n\r\n                <tr>\r\n            <td class=3D\"bb-pr-0\" style=3D\"font-f=\r\namily: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, R=\r\noboto, Helvetica Neue, sans-serif; padding-right: 0; padding: 4px 0; paddin=\r\ng-left: 0;\">\r\n                <a href=3D\"\" style=3D\"color: #206bc4; text-d=\r\necoration: none;\">\r\n                    <img src=3D\"https://martfury.gc/st=\r\norage/products/6-150x150.jpg\" class=3D\" bb-rounded\" width=3D\"64\" height=3D\"=\r\n64\" alt=3D\"\" style=3D\"line-height: 100%; outline: none; text-decoration: no=\r\nne; vertical-align: baseline; font-size: 0; border: 0 none; border-radius: =\r\n4px;\">\r\n                </a>\r\n            </td>\r\n            <td class=\r\n=3D\"bb-pl-md bb-w-100p\" style=3D\"font-family: Inter, -apple-system, BlinkMa=\r\ncSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; w=\r\nidth: 100%; padding-left: 16px !important; padding: 4px 0;\">\r\n            =\r\n    <strong style=3D\"font-weight: 600;\">Nikon HD camera</strong><br>\r\n    =\r\n                                <span class=3D\"bb-text-muted\" style=3D\"colo=\r\nr: #667382;\">(Color: Black, Size: XXL)</span>\r\n               =20\r\n        =\r\n                    </td>\r\n            <td class=3D\"bb-text-center\" style=\r\n=3D\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, S=\r\negoe UI, Roboto, Helvetica Neue, sans-serif; text-align: center; padding: 4=\r\npx 0;\">x 1</td>\r\n            <td class=3D\"bb-text-right\" style=3D\"font-fam=\r\nily: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Rob=\r\noto, Helvetica Neue, sans-serif; text-align: right; padding: 4px 0; padding=\r\n-right: 0;\">SAR480.00</td>\r\n        </tr>\r\n   =20\r\n                       =\r\n             <tr>\r\n                    <td colspan=3D\"2\" class=3D\"bb-borde=\r\nr-top bb-text-right\" style=3D\"font-family: Inter, -apple-system, BlinkMacSy=\r\nstemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text=\r\n-align: right; border-top: 1px solid #dce0e5; padding: 4px 0; padding-left:=\r\n 0;\">Subtotal</td>\r\n                    <td colspan=3D\"2\" class=3D\"bb-bord=\r\ner-top bb-text-right\" style=3D\"font-family: Inter, -apple-system, BlinkMacS=\r\nystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; tex=\r\nt-align: right; border-top: 1px solid #dce0e5; padding: 4px 0; padding-righ=\r\nt: 0;\">SAR480.00</td>\r\n                </tr>\r\n           =20\r\n            =\r\n\r\n                            <tr>\r\n                    <td colspan=3D\"2\"=\r\n class=3D\"bb-text-right\" style=3D\"font-family: Inter, -apple-system, BlinkM=\r\nacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; =\r\ntext-align: right; padding: 4px 0; padding-left: 0;\">Tax</td>\r\n           =\r\n         <td colspan=3D\"2\" class=3D\"bb-text-right\" style=3D\"font-family: In=\r\nter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, He=\r\nlvetica Neue, sans-serif; text-align: right; padding: 4px 0; padding-right:=\r\n 0;\">SAR48.00</td>\r\n                </tr>\r\n           =20\r\n           =20\r\n=\r\n                        <tr>\r\n                <td colspan=3D\"2\" class=3D\"b=\r\nb-text-right bb-font-strong bb-h3 bb-m-0\" style=3D\"font-family: Inter, -app=\r\nle-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica N=\r\neue, sans-serif; color: #232b42; font-size: 20px; line-height: 130%; text-a=\r\nlign: right; font-weight: 600; margin: 0; padding: 4px 0; padding-left: 0;\"=\r\n>Total</td>\r\n                <td colspan=3D\"2\" class=3D\"bb-font-strong bb-=\r\nh3 bb-m-0 bb-text-right\" style=3D\"font-family: Inter, -apple-system, BlinkM=\r\nacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; =\r\ncolor: #232b42; font-size: 20px; line-height: 130%; text-align: right; font=\r\n-weight: 600; margin: 0; padding: 4px 0; padding-right: 0;\">SAR528.00</td>=\r\n\r\n            </tr>\r\n            </tbody>\r\n</table>\r\n\r\n\r\n            =\r\n                        </td>\r\n            </tr>\r\n            <tr>\r\n    =\r\n            <td class=3D\"bb-content bb-border-top\" style=3D\"font-family: In=\r\nter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, He=\r\nlvetica Neue, sans-serif; padding: 40px 48px; border-top: 1px solid #dce0e5=\r\n;\">\r\n                    <table class=3D\"bb-row bb-mb-md\" cellpadding=3D\"0=\r\n\" cellspacing=3D\"0\" style=3D\"font-family: Inter, -apple-system, BlinkMacSys=\r\ntemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; borde=\r\nr-collapse: collapse; width: 100%; table-layout: fixed; margin-bottom: 16px=\r\n; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\r\n               =\r\n         <tbody>\r\n                            <tr>\r\n                     =\r\n           <td class=3D\"bb-bb-col\" style=3D\"font-family: Inter, -apple-syst=\r\nem, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sa=\r\nns-serif;\">\r\n                                    <h4 class=3D\"bb-m-0\" styl=\r\ne=3D\"font-weight: 600; color: #232b42; font-size: 16px; margin: 0;\">Order n=\r\number</h4>\r\n                                    <div>#10000052</div>\r\n   =\r\n                             </td>\r\n                                <td cl=\r\nass=3D\"bb-col-spacer\" style=3D\"font-family: Inter, -apple-system, BlinkMacS=\r\nystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; ver=\r\ntical-align: top; width: 24px;\"></td>\r\n                                <td=\r\n class=3D\"bb-col\" style=3D\"font-family: Inter, -apple-system, BlinkMacSyste=\r\nmFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; vertica=\r\nl-align: top;\">\r\n                                    <h4 class=3D\"bb-mb-0\"=\r\n style=3D\"font-weight: 600; margin: 0 0 0.5em; color: #232b42; font-size: 1=\r\n6px; margin-bottom: 0;\">Order date</h4>\r\n                                 =\r\n   <div>2025-08-15 16:37:22</div>\r\n                                </td>=\r\n\r\n                            </tr>\r\n                        </tbody>\r\n =\r\n                   </table>\r\n                    <table class=3D\"bb-row\" c=\r\nellpadding=3D\"0\" cellspacing=3D\"0\" style=3D\"font-family: Inter, -apple-syst=\r\nem, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sa=\r\nns-serif; border-collapse: collapse; width: 100%; table-layout: fixed; -pre=\r\nmailer-cellpadding: 0; -premailer-cellspacing: 0;\">\r\n                     =\r\n   <tbody>\r\n                            <tr>\r\n                           =\r\n     <td class=3D\"bb-col\" style=3D\"font-family: Inter, -apple-system, Blink=\r\nMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;=\r\n vertical-align: top;\">\r\n                                                 =\r\n                       <h4 class=3D\"bb-m-0\" style=3D\"font-weight: 600; colo=\r\nr: #232b42; font-size: 16px; margin: 0;\">Shipping Method</h4>\r\n           =\r\n                         <div>\r\n                                        Lo=\r\ncal Pickup\r\n                                    </div>\r\n                 =\r\n                                                   </td>\r\n\r\n             =\r\n                   <td class=3D\"bb-col-spacer\" style=3D\"font-family: Inter,=\r\n -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvet=\r\nica Neue, sans-serif; vertical-align: top; width: 24px;\"></td>\r\n          =\r\n                      <td class=3D\"bb-col\" style=3D\"font-family: Inter, -ap=\r\nple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica =\r\nNeue, sans-serif; vertical-align: top;\">\r\n                                =\r\n    <h4 class=3D\"bb-m-0\" style=3D\"font-weight: 600; color: #232b42; font-si=\r\nze: 16px; margin: 0;\">Payment Method</h4>\r\n                               =\r\n     <div>\r\n                                        HyperPay\r\n           =\r\n                         </div>\r\n                                </td>\r\n =\r\n                           </tr>\r\n                        </tbody>\r\n     =\r\n               </table>\r\n                </td>\r\n            </tr>\r\n     =\r\n   </tbody>\r\n    </table>\r\n</div>\r\n\r\n<table cellspacing=3D\"0\" cellpaddi=\r\nng=3D\"0\" style=3D\"font-family: Inter, -apple-system, BlinkMacSystemFont, Sa=\r\nn Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse:=\r\n collapse; width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: =\r\n0;\">\r\n    <tbody>\r\n        <tr>\r\n            <td class=3D\"bb-py-xl\" styl=\r\ne=3D\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, =\r\nSegoe UI, Roboto, Helvetica Neue, sans-serif; padding-top: 48px; padding-bo=\r\nttom: 48px;\">\r\n                <table class=3D\"bb-text-center bb-text-mute=\r\nd\" cellspacing=3D\"0\" cellpadding=3D\"0\" style=3D\"font-family: Inter, -apple-=\r\nsystem, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue=\r\n, sans-serif; border-collapse: collapse; width: 100%; color: #667382; text-=\r\nalign: center; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\r\n  =\r\n                  <tbody>\r\n                   =20\r\n                    <tr>=\r\n\r\n                        <td class=3D\"bb-px-lg\" style=3D\"font-family: Int=\r\ner, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Hel=\r\nvetica Neue, sans-serif; padding-right: 24px; padding-left: 24px;\">\r\n     =\r\n                       =C2=A9 2025 MartFury. All Rights Reserved.\r\n       =\r\n                 </td>\r\n                    </tr>\r\n\r\n                   =\r\n                         <tr>\r\n                            <td class=3D\"bb=\r\n-pt-md\" style=3D\"font-family: Inter, -apple-system, BlinkMacSystemFont, San=\r\n Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding-top: 16px=\r\n;\">\r\n                                If you have any questions, feel free =\r\nto message us at <a href=3D\"mailto:<EMAIL>\" style=3D\"color: #206bc=\r\n4; text-decoration: none;\"><EMAIL></a>.\r\n                        =\r\n    </td>\r\n                        </tr>\r\n                               =\r\n         </tbody>\r\n                </table>\r\n            </td>\r\n        =\r\n</tr>\r\n    </tbody>\r\n</table>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table>\r\n</t=\r\nd>\r\n</tr>\r\n</tbody>\r\n</table>\r\n</center>\r\n</body>\r\n\r\n</html>", "", "2025-08-15 16:38:06", "2025-08-15 16:38:06"], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "platform/plugins/fob-email-log/src/Listeners/EmailLogger.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\fob-email-log\\src\\Listeners\\EmailLogger.php", "line": 20}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Mail/Mailer.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\Mailer.php", "line": 617}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Mail/Mailer.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\Mailer.php", "line": 336}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Mail/Mailable.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\Mailable.php", "line": 207}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Mail/Mailable.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\Mailable.php", "line": 200}], "start": **********.642596, "duration": 0.0052, "duration_str": "5.2ms", "memory": 0, "memory_str": null, "filename": "EmailLogger.php:20", "source": {"index": 18, "namespace": null, "name": "platform/plugins/fob-email-log/src/Listeners/EmailLogger.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\fob-email-log\\src\\Listeners\\EmailLogger.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Ffob-email-log%2Fsrc%2FListeners%2FEmailLogger.php&line=20", "ajax": false, "filename": "EmailLogger.php", "line": "20"}, "connection": "martfury", "explain": null, "start_percent": 90.105, "width_percent": 3.825}, {"sql": "insert into `ec_order_histories` (`action`, `description`, `order_id`, `updated_at`, `created_at`) values ('create_order', 'New order #10000052 from <PERSON><PERSON>', 53, '2025-08-15 16:38:06', '2025-08-15 16:38:06')", "type": "query", "params": [], "bindings": [{"value": "create_order", "label": "create_order"}, "New order #10000052 from <PERSON><PERSON>", 53, "2025-08-15 16:38:06", "2025-08-15 16:38:06"], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 145}, {"index": 20, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 321}, {"index": 24, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 46}, {"index": 26, "namespace": null, "name": "platform/plugins/hyperpay/src/Http/Controllers/HyperPayController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\hyperpay\\src\\Http\\Controllers\\HyperPayController.php", "line": 84}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.651335, "duration": 0.00359, "duration_str": "3.59ms", "memory": 0, "memory_str": null, "filename": "OrderHelper.php:145", "source": {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 145}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FSupports%2FOrderHelper.php&line=145", "ajax": false, "filename": "OrderHelper.php", "line": "145"}, "connection": "martfury", "explain": null, "start_percent": 93.931, "width_percent": 2.641}, {"sql": "select `ec_flash_sales`.*, `ec_flash_sale_products`.`product_id` as `pivot_product_id`, `ec_flash_sale_products`.`flash_sale_id` as `pivot_flash_sale_id`, `ec_flash_sale_products`.`price` as `pivot_price`, `ec_flash_sale_products`.`quantity` as `pivot_quantity`, `ec_flash_sale_products`.`sold` as `pivot_sold` from `ec_flash_sales` inner join `ec_flash_sale_products` on `ec_flash_sales`.`id` = `ec_flash_sale_products`.`flash_sale_id` where `ec_flash_sale_products`.`product_id` = 6 and `status` = 'published' and date(`end_date`) >= '2025-08-15' and `ec_flash_sale_products`.`quantity` > sold order by `created_at` desc limit 1", "type": "query", "params": [], "bindings": [6, "published", "2025-08-15"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 183}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 321}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 46}, {"index": 24, "namespace": null, "name": "platform/plugins/hyperpay/src/Http/Controllers/HyperPayController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\hyperpay\\src\\Http\\Controllers\\HyperPayController.php", "line": 84}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.661587, "duration": 0.00398, "duration_str": "3.98ms", "memory": 0, "memory_str": null, "filename": "OrderHelper.php:183", "source": {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 183}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FSupports%2FOrderHelper.php&line=183", "ajax": false, "filename": "OrderHelper.php", "line": "183"}, "connection": "martfury", "explain": null, "start_percent": 96.572, "width_percent": 2.928}, {"sql": "select * from `ec_orders` where `ec_orders`.`id` = 53 limit 1", "type": "query", "params": [], "bindings": [53], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/plugins/hyperpay/src/Http/Controllers/HyperPayController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\hyperpay\\src\\Http\\Controllers\\HyperPayController.php", "line": 100}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.666686, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 99.5, "width_percent": 0.5}]}, "models": {"data": {"Botble\\Ecommerce\\Models\\Product": {"retrieved": 8, "updated": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\Order": {"retrieved": 4, "updated": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FOrder.php&line=1", "ajax": false, "filename": "Order.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\ProductVariation": {"retrieved": 6, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProductVariation.php&line=1", "ajax": false, "filename": "ProductVariation.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\Currency": {"retrieved": 5, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FCurrency.php&line=1", "ajax": false, "filename": "Currency.php", "line": "?"}}, "Botble\\Payment\\Models\\Payment": {"created": 1, "retrieved": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fpayment%2Fsrc%2FModels%2FPayment.php&line=1", "ajax": false, "filename": "Payment.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\OrderAddress": {"retrieved": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FOrderAddress.php&line=1", "ajax": false, "filename": "OrderAddress.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\OrderProduct": {"retrieved": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FOrderProduct.php&line=1", "ajax": false, "filename": "OrderProduct.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\Customer": {"retrieved": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\ShippingRule": {"retrieved": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FShippingRule.php&line=1", "ajax": false, "filename": "ShippingRule.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\OrderHistory": {"created": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FOrderHistory.php&line=1", "ajax": false, "filename": "OrderHistory.php", "line": "?"}}, "Botble\\Language\\Models\\Language": {"retrieved": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Flanguage%2Fsrc%2FModels%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}, "Botble\\ACL\\Models\\User": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Facl%2Fsrc%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\Invoice": {"created": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FInvoice.php&line=1", "ajax": false, "filename": "Invoice.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\InvoiceItem": {"created": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FInvoiceItem.php&line=1", "ajax": false, "filename": "InvoiceItem.php", "line": "?"}}, "Botble\\Base\\Models\\AdminNotification": {"created": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FAdminNotification.php&line=1", "ajax": false, "filename": "AdminNotification.php", "line": "?"}}, "Botble\\Marketplace\\Models\\Store": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FModels%2FStore.php&line=1", "ajax": false, "filename": "Store.php", "line": "?"}}, "Botble\\Slug\\Models\\Slug": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fslug%2Fsrc%2FModels%2FSlug.php&line=1", "ajax": false, "filename": "Slug.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\Shipment": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FShipment.php&line=1", "ajax": false, "filename": "Shipment.php", "line": "?"}}, "FriendsOfBotble\\EmailLog\\Models\\EmailLog": {"created": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Ffob-email-log%2Fsrc%2FModels%2FEmailLog.php&line=1", "ajax": false, "filename": "EmailLog.php", "line": "?"}}}, "count": 52, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 41, "created": 7, "updated": 4}}, "symfonymailer_mails": {"count": 1, "mails": [{"to": ["<EMAIL>"], "subject": "New order(s) at MartFury", "headers": "From: Example <<EMAIL>>\r\nTo: kamron.k<PERSON><PERSON>@example.org\r\nSubject: New order(s) at MartFury\r\n", "body": null, "html": "<!doctype html>\n<html lang=\"en\">\n\n<head>\n    <meta charset=\"UTF-8\">\n    <title>MartFury</title>\n</head>\n\n<body class=\"bb-bg-body\" dir=\"ltr\" style=\"margin: 0; padding: 0; font-size: 14px; line-height: 171.4285714286%; mso-line-height-rule: exactly; color: #3A4859; width: 100%; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale; -webkit-font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11'; font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11'; font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; background-color: #f6f7f9;\">\n\n<center>\n    <table class=\"bb-main bb-bg-body\" width=\"100%\" cellspacing=\"0\" cellpadding=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; background-color: #f6f7f9;\">\n        <tbody>\n            <tr>\n                <td align=\"center\" valign=\"top\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;\">\n                    <table class=\"bb-wrap\" cellspacing=\"0\" cellpadding=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; max-width: 640px; text-align: left; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\n                        <tbody>\n                            <tr>\n                                <td class=\"bb-p-sm\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 8px;\">\n                                    <table cellpadding=\"0\" cellspacing=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\n                                        <tbody>\n                                            <tr>\n                                                <td class=\"bb-py-lg\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding-top: 24px; padding-bottom: 24px;\">\n                                                    <table cellspacing=\"0\" cellpadding=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\n                                                        <tbody>\n                                                            <tr>\n                                                                <td class=\"bb-text-left\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: left;\">\n                                                                    <a href=\"https://martfury.gc\" style=\"color: #206bc4; text-decoration: none;\">\n                                                                        <img class=\"bb-logo\" src=\"https://martfury.gc/storage/general/logo.png\" alt=\"MartFury\" style=\"line-height: 100%; outline: none; text-decoration: none; vertical-align: baseline; font-size: 0; border: 0 none; max-height: 40px;\">\n                                                                    </a>\n                                                                </td>\n                                                                <td class=\"bb-text-right\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right;\">\n                                                                    2025-08-15 16:38:06\n                                                                </td>\n                                                            </tr>\n                                                        </tbody>\n                                                    </table>\n                                                </td>\n                                            </tr>\n                                        </tbody>\n                                    </table>\n\n\n<div class=\"bb-main-content\">\n    <table class=\"bb-box\" cellpadding=\"0\" cellspacing=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; background: #ffffff; border-radius: 4px; -webkit-box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05); box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05); border: 1px solid #dce0e5; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\n        <tbody>\n            <tr>\n                <td class=\"bb-content bb-pb-0\" align=\"center\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 40px 48px; padding-bottom: 0;\">\n                    <table class=\"bb-icon bb-icon-lg bb-bg-blue\" cellspacing=\"0\" cellpadding=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 0; border-radius: 50%; background: #edeef0; line-height: 100%; font-weight: 300; border-collapse: separate; text-align: center; width: 72px; height: 72px; font-size: 48px; background-color: #206bc4; color: #ffffff; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\n                        <tbody>\n                            <tr>\n                                <td valign=\"middle\" align=\"center\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;\">\n                                    <img src=\"https://martfury.gc/vendor/core/core/base/images/email-icons/shopping-cart.png\" class=\"bb-va-middle\" width=\"40\" height=\"40\" alt=\"Icon\" style=\"border: 0 none; line-height: 100%; outline: none; text-decoration: none; font-size: 0; vertical-align: middle; display: block; width: 40px; height: 40px;\">\n                                </td>\n                            </tr>\n                        </tbody>\n                    </table>\n                    <h1 class=\"bb-text-center bb-m-0 bb-mt-md\" style=\"font-weight: 600; color: #232b42; font-size: 28px; line-height: 130%; text-align: center; margin: 0; margin-top: 16px;\">Order successfully!</h1>\n                </td>\n            </tr>\n            <tr>\n                <td class=\"bb-content\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 40px 48px;\">\n                    <div>Dear Young Shop,</div>\n                    <div>You got a new order on MartFury!</div>\n                </td>\n            </tr>\n            <tr>\n                <td class=\"bb-content bb-pt-0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 40px 48px; padding-top: 0;\">\n                    <table class=\"bb-row bb-mb-md\" cellpadding=\"0\" cellspacing=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; table-layout: fixed; margin-bottom: 16px; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\n                        <tbody>\n                            <tr>\n                                <td class=\"bb-bb-col\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;\">\n                                    <h4 class=\"bb-m-0\" style=\"font-weight: 600; color: #232b42; font-size: 16px; margin: 0;\">Customer Information</h4>\n                                    <div>Name: <strong style=\"font-weight: 600;\">Fae Koelpin</strong>\n</div>\n                                                                        <div>Phone: <strong style=\"font-weight: 600;\">+18598017656</strong>\n</div>\n                                                                                                                                                <div>Address: <strong style=\"font-weight: 600;\">2358 Jadon Stream, Bashirianfurt, Utah, PE</strong>\n</div>\n                                                                    </td>\n                            </tr>\n                        </tbody>\n                    </table>\n                </td>\n            </tr>\n            <tr>\n                <td class=\"bb-content bb-pt-0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 40px 48px; padding-top: 0;\">\n                    <h4 style=\"font-weight: 600; margin: 0 0 0.5em; color: #232b42; font-size: 16px;\">Here's what you ordered:</h4>\n                    <a class=\"button button-blue\" href=\"https://martfury.gc/orders/tracking?order_id=%2310000052&amp;email=vendor%40botble.com\" style=\"color: #206bc4; text-decoration: none;\">View order</a>\n    or <a href=\"https://martfury.gc\" style=\"color: #206bc4; text-decoration: none;\">Go to our shop</a>\n\n    <br>\n\n<table class=\"bb-table\" cellspacing=\"0\" cellpadding=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\n    <thead>\n        <tr>\n            <th colspan=\"2\" style=\"text-transform: uppercase; font-weight: 600; color: #667382; font-size: 12px; padding: 0 0 4px 0; padding-left: 0;\"></th>\n            <th style=\"text-transform: uppercase; font-weight: 600; color: #667382; font-size: 12px; padding: 0 0 4px 0;\">Quantity</th>\n            <th class=\"bb-text-right\" style=\"text-align: right; text-transform: uppercase; font-weight: 600; color: #667382; font-size: 12px; padding: 0 0 4px 0; padding-right: 0;\">Price</th>\n        </tr>\n    </thead>\n\n    <tbody>\n                <tr>\n            <td class=\"bb-pr-0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding-right: 0; padding: 4px 0; padding-left: 0;\">\n                <a href=\"\" style=\"color: #206bc4; text-decoration: none;\">\n                    <img src=\"https://martfury.gc/storage/products/6-150x150.jpg\" class=\" bb-rounded\" width=\"64\" height=\"64\" alt=\"\" style=\"line-height: 100%; outline: none; text-decoration: none; vertical-align: baseline; font-size: 0; border: 0 none; border-radius: 4px;\">\n                </a>\n            </td>\n            <td class=\"bb-pl-md bb-w-100p\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; width: 100%; padding-left: 16px !important; padding: 4px 0;\">\n                <strong style=\"font-weight: 600;\">Nikon HD camera</strong><br>\n                                    <span class=\"bb-text-muted\" style=\"color: #667382;\">(Color: Black, Size: XXL)</span>\n                \n                            </td>\n            <td class=\"bb-text-center\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: center; padding: 4px 0;\">x 1</td>\n            <td class=\"bb-text-right\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right; padding: 4px 0; padding-right: 0;\">SAR480.00</td>\n        </tr>\n    \n                                    <tr>\n                    <td colspan=\"2\" class=\"bb-border-top bb-text-right\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right; border-top: 1px solid #dce0e5; padding: 4px 0; padding-left: 0;\">Subtotal</td>\n                    <td colspan=\"2\" class=\"bb-border-top bb-text-right\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right; border-top: 1px solid #dce0e5; padding: 4px 0; padding-right: 0;\">SAR480.00</td>\n                </tr>\n            \n            \n                            <tr>\n                    <td colspan=\"2\" class=\"bb-text-right\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right; padding: 4px 0; padding-left: 0;\">Tax</td>\n                    <td colspan=\"2\" class=\"bb-text-right\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right; padding: 4px 0; padding-right: 0;\">SAR48.00</td>\n                </tr>\n            \n            \n                        <tr>\n                <td colspan=\"2\" class=\"bb-text-right bb-font-strong bb-h3 bb-m-0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; color: #232b42; font-size: 20px; line-height: 130%; text-align: right; font-weight: 600; margin: 0; padding: 4px 0; padding-left: 0;\">Total</td>\n                <td colspan=\"2\" class=\"bb-font-strong bb-h3 bb-m-0 bb-text-right\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; color: #232b42; font-size: 20px; line-height: 130%; text-align: right; font-weight: 600; margin: 0; padding: 4px 0; padding-right: 0;\">SAR528.00</td>\n            </tr>\n            </tbody>\n</table>\n\n\n                                    </td>\n            </tr>\n            <tr>\n                <td class=\"bb-content bb-border-top\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 40px 48px; border-top: 1px solid #dce0e5;\">\n                    <table class=\"bb-row bb-mb-md\" cellpadding=\"0\" cellspacing=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; table-layout: fixed; margin-bottom: 16px; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\n                        <tbody>\n                            <tr>\n                                <td class=\"bb-bb-col\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;\">\n                                    <h4 class=\"bb-m-0\" style=\"font-weight: 600; color: #232b42; font-size: 16px; margin: 0;\">Order number</h4>\n                                    <div>#10000052</div>\n                                </td>\n                                <td class=\"bb-col-spacer\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; vertical-align: top; width: 24px;\"></td>\n                                <td class=\"bb-col\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; vertical-align: top;\">\n                                    <h4 class=\"bb-mb-0\" style=\"font-weight: 600; margin: 0 0 0.5em; color: #232b42; font-size: 16px; margin-bottom: 0;\">Order date</h4>\n                                    <div>2025-08-15 16:37:22</div>\n                                </td>\n                            </tr>\n                        </tbody>\n                    </table>\n                    <table class=\"bb-row\" cellpadding=\"0\" cellspacing=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; table-layout: fixed; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\n                        <tbody>\n                            <tr>\n                                <td class=\"bb-col\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; vertical-align: top;\">\n                                                                        <h4 class=\"bb-m-0\" style=\"font-weight: 600; color: #232b42; font-size: 16px; margin: 0;\">Shipping Method</h4>\n                                    <div>\n                                        Local Pickup\n                                    </div>\n                                                                    </td>\n\n                                <td class=\"bb-col-spacer\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; vertical-align: top; width: 24px;\"></td>\n                                <td class=\"bb-col\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; vertical-align: top;\">\n                                    <h4 class=\"bb-m-0\" style=\"font-weight: 600; color: #232b42; font-size: 16px; margin: 0;\">Payment Method</h4>\n                                    <div>\n                                        HyperPay\n                                    </div>\n                                </td>\n                            </tr>\n                        </tbody>\n                    </table>\n                </td>\n            </tr>\n        </tbody>\n    </table>\n</div>\n\n<table cellspacing=\"0\" cellpadding=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\n    <tbody>\n        <tr>\n            <td class=\"bb-py-xl\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding-top: 48px; padding-bottom: 48px;\">\n                <table class=\"bb-text-center bb-text-muted\" cellspacing=\"0\" cellpadding=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; color: #667382; text-align: center; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\n                    <tbody>\n                    \n                    <tr>\n                        <td class=\"bb-px-lg\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding-right: 24px; padding-left: 24px;\">\n                            © 2025 MartFury. All Rights Reserved.\n                        </td>\n                    </tr>\n\n                                            <tr>\n                            <td class=\"bb-pt-md\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding-top: 16px;\">\n                                If you have any questions, feel free to message us at <a href=\"mailto:<EMAIL>\" style=\"color: #206bc4; text-decoration: none;\"><EMAIL></a>.\n                            </td>\n                        </tr>\n                                        </tbody>\n                </table>\n            </td>\n        </tr>\n    </tbody>\n</table>\n</td>\n</tr>\n</tbody>\n</table>\n</td>\n</tr>\n</tbody>\n</table>\n</center>\n</body>\n\n</html>"}]}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "302 Found", "full_url": "https://martfury.gc/payment/hyperpay/callback?id=460F3D223F84416536B101539705D3A8.uat01-vm-tx02&reso...", "action_name": "payments.hyperpay.callback", "controller_action": "Botble\\HyperPay\\Http\\Controllers\\HyperPayController@callback", "uri": "GET payment/hyperpay/callback", "controller": "Botble\\HyperPay\\Http\\Controllers\\HyperPayController@callback<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fhyperpay%2Fsrc%2FHttp%2FControllers%2FHyperPayController.php&line=60\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "Botble\\HyperPay\\Http\\Controllers", "prefix": "/payment/hyperpay", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fhyperpay%2Fsrc%2FHttp%2FControllers%2FHyperPayController.php&line=60\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">platform/plugins/hyperpay/src/Http/Controllers/HyperPayController.php:60-186</a>", "middleware": "web, core", "duration": "2.42s", "peak_memory": "62MB", "response": "Redirect to https://martfury.gc/checkout/2bbdbd08f9ec9fc36d8b3e7ced0cf369?error=1&amp;error_type=payment", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-2144133382 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"46 characters\">460F3D223F84416536B101539705D3A8.uat01-vm-tx02</span>\"\n  \"<span class=sf-dump-key>resourcePath</span>\" => \"<span class=sf-dump-str title=\"68 characters\">/v1/checkouts/460F3D223F84416536B101539705D3A8.uat01-vm-tx02/payment</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2144133382\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-724689496 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-724689496\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1761360128 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">martfury.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"110 characters\">https://martfury.gc/payment/hyperpay/checkout/460F3D223F84416536B101539705D3A8.uat01-vm-tx02?payment_type=visa</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3215 characters\">cookie_for_consent=1; botble_footprints_cookie=eyJpdiI6Ilp2Q1lFM2s1MkVpWnI2blVTb2FtZWc9PSIsInZhbHVlIjoiU1N3dm1pVUNyMlJieWRscUU2Rkk3dzFSVmsxSFhpU3Bpc0lSQmRvOEl3M0ZwaW42RkFpdUoxV1NOWW52Q2lOWFh1WUVZUFVpWnBGdHBuc2xVWXV6TmJFRUd6VVljQ25vWWlDaVhBczVpNEpTU0NmbytXczNaTzdDdFNCN3U4anoiLCJtYWMiOiJjZDdlODUyY2JjNzc4MjNmNmEwMTc4YzA3ODFlM2MyNmQ0MzhjMGNkMTA2ZDY3MjYwYjQ0MmMzMjA1NDAzMjhjIiwidGFnIjoiIn0%3D; botble_footprints_cookie_data=eyJpdiI6ImNlT05BQnNiOExZUk94aE9NbHNkYWc9PSIsInZhbHVlIjoiTWVoMVdkQWFROThBdm9VWUV1cWd6TFlLUFR2emErYW5PWWNDUk5ycVBWOURCdzVHT0pRaEpET242bzEzSFh0bWlpT0RZZnoxVUd4U0swOGUyV1lteXU0SDJhdG80U0VhdnVHaFVFVUJ0amhRVFhTOEFMWUpnYmVmamdqQ2NrNERDMW1lbElBeWNYN2NHQTAyZm9PTk5FKzIvT3VjRjU3ZU40bjJVNGI5WmdSTHBoZTZrUnp0RE9MbEg0aytxMUVlNTlJTW9lZnJGVWxDbHI3Q3poR2RDbzVISnB1Y0pCSXRDb0cyYmFCRUVRMkl2RkxUQlpoanhhR2pINW1ZdkZmS091REtuSnRlNjB6ekR2clEweFZlMjh1M0dOanZQdTlpcHdTRlg2YzJxQm92VHRPdG9SekNPSEJVa0VKcjlEZjlJenVONVVwZTJnRjQ4N1JZKzhEd2pnZHJsUkM3eXVaMmhYblhRZmVoQlk0YTNSOFFYUEhqYi9hQzZIWkloa0llWGNaMEFYVFFmVGhnYnZJcnJIUHh0Z2pzREpUb2ptM1BnU25wV3M3eWF2RUl5UVdwU0UzUDdCZkErMXlCREdRSXExTzVVK1kwL0Jmd0laTGZ1VE9sc1BMbG5lVnVLNytLaEs1YjJpTEllK3Qwd3VUbHBEL3JLNlo0VEMwN2UxakRKK2hxUE9QTmI5TDdqeW03WkgwZjVnPT0iLCJtYWMiOiI1ZmIxN2YwMTBlNWZjMTY0MTY0NDMzZDUyZmZjNmQ5ZWZkYjk0NTYxZjg0ZTk4ODAzNTVlYzJhYWJmN2I0NmVhIiwidGFnIjoiIn0%3D; shortcode_cache_suggestion_dismissed=1; widget_cache_suggestion_dismissed=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Imtmd01aTUlkUmlvaEJqSmRGMnJ5aXc9PSIsInZhbHVlIjoiMGVSOUY2VWtsSitnWmpMdXIxbHd1WC9PeGRISXNyNXQwM1dJeXBwUVZweUphTlViRWNWT09zVXpYUVpNWm1nTEJXeDlxQmlZTDUrOUNZdk9POERqSzQ0V0N4NXhGMFlyd1hPa2xKVGZjTjhWSWFQbWpRcmdGT1ViRFEyQ2IrMjUwMXNtOW1hbWM0MU5xR2UwdmkycTUyTlNibmRhdENJeXZnYkVlQlFlSkZYVm9BZk43WXU0RUhtOWt5NFZNVTJlN3BSMDRkaUd2Z0RwNzBqei9RUDZRVVhpWm5idEpKK3dJTWJ2dVg1OVQ0Zz0iLCJtYWMiOiJlZTQ5Mzc4NzBkNjk4ODUxYjJmNzI5ZDljM2E1OGY4NzM4YzUyYzc1MGFhYzgwMGY2NjJmM2JlN2Q5OWUwODJmIiwidGFnIjoiIn0%3D; botble_cookie_newsletter=1; remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpibjFnQzQrTDFHeWZNOTFoK1F4R2c9PSIsInZhbHVlIjoiWlBCZ2pyclRiZU9razRVZjF3YU1JVjl3ZlcrTVZKVW9JVlplS2lQOStvUVdjM0RCeWx3a2YrOEpicWtScGdBcXJtY1hZZ3BJOTdhR2dHdmRPYXB0VlZjcG5Mb1hjYWc3TnNxT1Z3RVJZaEM0VXRsSjMvWHp1VTIzR3gvTmJtbUh1dlhiSFhjZzZqMU1JVUVjRVQwZURXeUt5eGFuclh4TVFlSkdyeFQzMVkvdGhRWVlsenZPSG8rNWRNUVU5RG9KYUh6S1YwWmVwOUtCTWd5ckROdnpkZ3gxeTJ1WWdpR29mMUJ6TUpRUTdHQT0iLCJtYWMiOiI3M2Q5NDM1Y2Q2ZWU1NTMzNzczMjdhYWM3ZmRjNTJhZTFjNThiNTFlOTllZGZhM2RlOTIwNWI0OWI1M2U2MGI0IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImJoYzRzR1B0UGlNZk5TSGJyelVObGc9PSIsInZhbHVlIjoiWGhQczNZNHhVbXdMb2VvY2ttM2dJeVRPeGhOMjBVWU4zU0hkMGUzaEwwa0xOaE5ENGM0Q2NkZk1zYWVINGw1NkpFcXRiVDd2N3Bjb3dxT2kyaThwNmpucldRSWJGN1d2RTlDRXBwc2IzTWRDUXFmSkQwSEwxSVIvV0VyVlFCNm8iLCJtYWMiOiIxNjZhYWUyZmU0MjMxNzJiODhkZGU4MzNjMGNkZDRmMjIxN2Q3ZGFlNzA5MGQwMjk5NzUxNGUxNGFlZTUxOGUxIiwidGFnIjoiIn0%3D; botble_session=eyJpdiI6IkVsR3NvNnJrNmxhRWlLamtiempWbHc9PSIsInZhbHVlIjoiSFZoeVk0ZW5HSC90TWxKdjcwelZDM0ZLRWU1UmRydUp5bHc0eVJJMEJEbVo2VDlQSVozaGlDTVpHMXoyOW9BYzVpNEJ4dzA2MFFZcjcxUE41dDM2ak15RUttSmpFTmxKSWtQK2grbjg5eTJUVG8vZURWRHlIWGFTdnZqTGp0MG4iLCJtYWMiOiJjYjBjODk0N2Y5ODczODQ2NDFkZDk1ZmNmNDdhOTcxZWRiNzU3NDg4ZGQ5YTg1NDM1NzI0ZjgwNjg4NWQ4NTA4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1761360128\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1169393863 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>botble_footprints_cookie</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5d4a7afb407de91a3f785dca41f29624d4035861</span>\"\n  \"<span class=sf-dump-key>botble_footprints_cookie_data</span>\" => \"<span class=sf-dump-str title=\"347 characters\">{&quot;footprint&quot;:&quot;5d4a7afb407de91a3f785dca41f29624d4035861&quot;,&quot;ip&quot;:&quot;127.0.0.1&quot;,&quot;landing_domain&quot;:&quot;martfury.gc&quot;,&quot;landing_page&quot;:&quot;\\/&quot;,&quot;landing_params&quot;:null,&quot;referral&quot;:null,&quot;gclid&quot;:null,&quot;fclid&quot;:null,&quot;utm_source&quot;:null,&quot;utm_campaign&quot;:null,&quot;utm_medium&quot;:null,&quot;utm_term&quot;:null,&quot;utm_content&quot;:null,&quot;referrer_url&quot;:&quot;http:\\/\\/localhost\\/&quot;,&quot;referrer_domain&quot;:&quot;localhost&quot;}</span>\"\n  \"<span class=sf-dump-key>shortcode_cache_suggestion_dismissed</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>widget_cache_suggestion_dismissed</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|mr2hzXolFPMrElNypPbMsV1VAiAKgY80Q2lBNyezSZXf4Bow8DqaNYnj6c6c|$2y$12$faChkoM7UV9t8mCoo6oJFOLYCOlwKR0CpAW2E1NPY0Bj1MIeVQw92</span>\"\n  \"<span class=sf-dump-key>botble_cookie_newsletter</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">2|rEY2fnBoxjVikI7FHOoHpfsUL6HmShbqbiBbMajXCGvqp4DJneBXZNsMDEhJ|$2y$12$mAIYinlq8PEmipS0gyL5O.c3YY4axUf.8ExV94mmLdgzhultxp2KS</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KzcOpLY5BY4Qmb2ysxF4UMkeFxNzIwhg5XNVcWQ7</span>\"\n  \"<span class=sf-dump-key>botble_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">rOB2sRYY2tEGNk5tY11YcouH4tG83UStulFD0FGr</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1169393863\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2145424252 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 15 Aug 2025 16:38:06 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"88 characters\">https://martfury.gc/checkout/2bbdbd08f9ec9fc36d8b3e7ced0cf369?error=1&amp;error_type=payment</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2145424252\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-68249573 data-indent-pad=\"  \"><span class=sf-dump-note>array:22</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KzcOpLY5BY4Qmb2ysxF4UMkeFxNzIwhg5XNVcWQ7</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>math-captcha</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>first</span>\" => <span class=sf-dump-num>4</span>\n    \"<span class=sf-dump-key>second</span>\" => <span class=sf-dump-num>8</span>\n    \"<span class=sf-dump-key>operand</span>\" => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"110 characters\">https://martfury.gc/payment/hyperpay/checkout/460F3D223F84416536B101539705D3A8.uat01-vm-tx02?payment_type=visa</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">error_msg</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>cart</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>cart_updated_at</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @1755275859\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @1755275859</span></span> {<a class=sf-dump-ref href=#sf-dump-68249573-ref24647 title=\"3 occurrences\">#4647</a><samp data-depth=3 id=sf-dump-68249573-ref24647 class=sf-dump-compact>\n      #<span class=sf-dump-protected title=\"Protected property\">endOfTime</span>: <span class=sf-dump-const>false</span>\n      #<span class=sf-dump-protected title=\"Protected property\">startOfTime</span>: <span class=sf-dump-const>false</span>\n      #<span class=sf-dump-protected title=\"Protected property\">constructedObjectId</span>: \"<span class=sf-dump-str title=\"32 characters\">00000000000012270000000000000000</span>\"\n      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Carbon\\Carbon`\">clock</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localMonthsOverflow</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localYearsOverflow</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localStrictModeEnabled</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localHumanDiffOptions</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localToStringFormat</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localSerializer</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localMacros</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localGenericMacros</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localFormatFunction</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localTranslator</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">dumpProperties</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">timezone_type</span>\"\n        <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">timezone</span>\"\n      </samp>]\n      #<span class=sf-dump-protected title=\"Protected property\">dumpLocale</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">dumpDateProperties</span>: <span class=sf-dump-const>null</span>\n      <span class=sf-dump-meta>date</span>: <span class=sf-dump-const title=\"Friday, August 15, 2025\n- 00:00:27.204671 from now\nDST Off\">2025-08-15 16:37:39.480986 UTC (+00:00)</span>\n    </samp>}\n  </samp>]\n  \"<span class=sf-dump-key>tracked_start_checkout</span>\" => \"<span class=sf-dump-str title=\"32 characters\">2bbdbd08f9ec9fc36d8b3e7ced0cf369</span>\"\n  \"<span class=sf-dump-key>2162c5409405ee8ecb45d303ef2ad5a8</span>\" => <span class=sf-dump-note>array:17</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>promotion_discount_amount</span>\" => <span class=sf-dump-num>0</span>\n    \"<span class=sf-dump-key>billing_address_same_as_shipping_address</span>\" => <span class=sf-dump-const>true</span>\n    \"<span class=sf-dump-key>billing_address</span>\" => []\n    \"<span class=sf-dump-key>marketplace</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:28</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>billing_address_same_as_shipping_address</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>billing_address</span>\" => []\n        \"<span class=sf-dump-key>created_order</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @1755275859\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @1755275859</span></span> {<a class=sf-dump-ref href=#sf-dump-68249573-ref24647 title=\"3 occurrences\">#4647</a>}\n        \"<span class=sf-dump-key>created_order_id</span>\" => <span class=sf-dump-num>53</span>\n        \"<span class=sf-dump-key>is_save_order_shipping_address</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>created_order_product</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @1755275859\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @1755275859</span></span> {<a class=sf-dump-ref href=#sf-dump-68249573-ref24647 title=\"3 occurrences\">#4647</a>}\n        \"<span class=sf-dump-key>coupon_discount_amount</span>\" => <span class=sf-dump-num>0</span>\n        \"<span class=sf-dump-key>applied_coupon_code</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>is_free_shipping</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>promotion_discount_amount</span>\" => <span class=sf-dump-num>0</span>\n        \"<span class=sf-dump-key>shipping_method</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n        \"<span class=sf-dump-key>shipping_option</span>\" => \"<span class=sf-dump-str>3</span>\"\n        \"<span class=sf-dump-key>shipping_amount</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"\n        \"<span class=sf-dump-key>shipping</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>default</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Local Pickup</span>\"\n              \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"\n            </samp>]\n            <span class=sf-dump-key>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Flat Rate</span>\"\n              \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">20.00</span>\"\n            </samp>]\n          </samp>]\n        </samp>]\n        \"<span class=sf-dump-key>default_shipping_method</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n        \"<span class=sf-dump-key>default_shipping_option</span>\" => \"<span class=sf-dump-str>3</span>\"\n        \"<span class=sf-dump-key>is_available_shipping</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>address_id</span>\" => <span class=sf-dump-num>3</span>\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Fae Koelpin</span>\"\n        \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n        \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"12 characters\">+13198941196</span>\"\n        \"<span class=sf-dump-key>country</span>\" => \"<span class=sf-dump-str title=\"2 characters\">PE</span>\"\n        \"<span class=sf-dump-key>state</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Utah</span>\"\n        \"<span class=sf-dump-key>city</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Bashirianfurt</span>\"\n        \"<span class=sf-dump-key>address</span>\" => \"<span class=sf-dump-str title=\"17 characters\">2358 Jadon Stream</span>\"\n        \"<span class=sf-dump-key>zip_code</span>\" => \"<span class=sf-dump-str title=\"10 characters\">09306-7143</span>\"\n        \"<span class=sf-dump-key>created_order_address</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>created_order_address_id</span>\" => <span class=sf-dump-num>47</span>\n      </samp>]\n      <span class=sf-dump-key>1</span> => <span class=sf-dump-note>array:28</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Fae Koelpin</span>\"\n        \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n        \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"12 characters\">+13198941196</span>\"\n        \"<span class=sf-dump-key>address</span>\" => \"<span class=sf-dump-str title=\"17 characters\">2358 Jadon Stream</span>\"\n        \"<span class=sf-dump-key>country</span>\" => \"<span class=sf-dump-str title=\"2 characters\">PE</span>\"\n        \"<span class=sf-dump-key>state</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Utah</span>\"\n        \"<span class=sf-dump-key>city</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Bashirianfurt</span>\"\n        \"<span class=sf-dump-key>zip_code</span>\" => \"<span class=sf-dump-str title=\"10 characters\">09306-7143</span>\"\n        \"<span class=sf-dump-key>created_order</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @1755274270\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @1755274270</span></span> {<a class=sf-dump-ref href=#sf-dump-68249573-ref24653 title=\"2 occurrences\">#4653</a><samp data-depth=5 id=sf-dump-68249573-ref24653 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">endOfTime</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">startOfTime</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">constructedObjectId</span>: \"<span class=sf-dump-str title=\"32 characters\">000000000000122d0000000000000000</span>\"\n          -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Carbon\\Carbon`\">clock</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localMonthsOverflow</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localYearsOverflow</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localStrictModeEnabled</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localHumanDiffOptions</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localToStringFormat</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localSerializer</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localMacros</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localGenericMacros</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localFormatFunction</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localTranslator</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">dumpProperties</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n            <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">timezone_type</span>\"\n            <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">timezone</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">dumpLocale</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">dumpDateProperties</span>: <span class=sf-dump-const>null</span>\n          <span class=sf-dump-meta>date</span>: <span class=sf-dump-const title=\"Friday, August 15, 2025\n- 00:26:56.506187 from now\nDST Off\">2025-08-15 16:11:10.179864 UTC (+00:00)</span>\n        </samp>}\n        \"<span class=sf-dump-key>created_order_id</span>\" => <span class=sf-dump-num>52</span>\n        \"<span class=sf-dump-key>is_save_order_shipping_address</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>created_order_address</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>created_order_address_id</span>\" => <span class=sf-dump-num>46</span>\n        \"<span class=sf-dump-key>created_order_product</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @1755274270\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @1755274270</span></span> {<a class=sf-dump-ref href=#sf-dump-68249573-ref24653 title=\"2 occurrences\">#4653</a>}\n        \"<span class=sf-dump-key>billing_address_same_as_shipping_address</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>billing_address</span>\" => []\n        \"<span class=sf-dump-key>coupon_discount_amount</span>\" => <span class=sf-dump-num>0</span>\n        \"<span class=sf-dump-key>applied_coupon_code</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>is_free_shipping</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>promotion_discount_amount</span>\" => <span class=sf-dump-num>0</span>\n        \"<span class=sf-dump-key>shipping_method</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n        \"<span class=sf-dump-key>shipping_option</span>\" => \"<span class=sf-dump-str>3</span>\"\n        \"<span class=sf-dump-key>shipping_amount</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"\n        \"<span class=sf-dump-key>shipping</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>default</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Local Pickup</span>\"\n              \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"\n            </samp>]\n            <span class=sf-dump-key>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Flat Rate</span>\"\n              \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">20.00</span>\"\n            </samp>]\n          </samp>]\n        </samp>]\n        \"<span class=sf-dump-key>default_shipping_method</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n        \"<span class=sf-dump-key>default_shipping_option</span>\" => \"<span class=sf-dump-str>3</span>\"\n        \"<span class=sf-dump-key>is_available_shipping</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>address_id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      </samp>]\n      <span class=sf-dump-key>2</span> => <span class=sf-dump-note>array:28</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>billing_address_same_as_shipping_address</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>billing_address</span>\" => []\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Fae Koelpin</span>\"\n        \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n        \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"12 characters\">+13198941196</span>\"\n        \"<span class=sf-dump-key>address</span>\" => \"<span class=sf-dump-str title=\"17 characters\">2358 Jadon Stream</span>\"\n        \"<span class=sf-dump-key>country</span>\" => \"<span class=sf-dump-str title=\"2 characters\">PE</span>\"\n        \"<span class=sf-dump-key>state</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Utah</span>\"\n        \"<span class=sf-dump-key>city</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Bashirianfurt</span>\"\n        \"<span class=sf-dump-key>zip_code</span>\" => \"<span class=sf-dump-str title=\"10 characters\">09306-7143</span>\"\n        \"<span class=sf-dump-key>address_id</span>\" => \"<span class=sf-dump-str>3</span>\"\n        \"<span class=sf-dump-key>created_order</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @1755274809\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @1755274809</span></span> {<a class=sf-dump-ref href=#sf-dump-68249573-ref24654 title=\"2 occurrences\">#4654</a><samp data-depth=5 id=sf-dump-68249573-ref24654 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">endOfTime</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">startOfTime</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">constructedObjectId</span>: \"<span class=sf-dump-str title=\"32 characters\">000000000000122e0000000000000000</span>\"\n          -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Carbon\\Carbon`\">clock</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localMonthsOverflow</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localYearsOverflow</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localStrictModeEnabled</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localHumanDiffOptions</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localToStringFormat</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localSerializer</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localMacros</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localGenericMacros</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localFormatFunction</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">localTranslator</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">dumpProperties</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n            <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">timezone_type</span>\"\n            <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">timezone</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">dumpLocale</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">dumpDateProperties</span>: <span class=sf-dump-const>null</span>\n          <span class=sf-dump-meta>date</span>: <span class=sf-dump-const title=\"Friday, August 15, 2025\n- 00:17:57.350552 from now\nDST Off\">2025-08-15 16:20:09.335562 UTC (+00:00)</span>\n        </samp>}\n        \"<span class=sf-dump-key>created_order_id</span>\" => <span class=sf-dump-num>51</span>\n        \"<span class=sf-dump-key>is_save_order_shipping_address</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>created_order_address</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>created_order_address_id</span>\" => <span class=sf-dump-num>45</span>\n        \"<span class=sf-dump-key>created_order_product</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @1755274809\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @1755274809</span></span> {<a class=sf-dump-ref href=#sf-dump-68249573-ref24654 title=\"2 occurrences\">#4654</a>}\n        \"<span class=sf-dump-key>coupon_discount_amount</span>\" => <span class=sf-dump-num>0</span>\n        \"<span class=sf-dump-key>applied_coupon_code</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>is_free_shipping</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>promotion_discount_amount</span>\" => <span class=sf-dump-num>0</span>\n        \"<span class=sf-dump-key>shipping_method</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n        \"<span class=sf-dump-key>shipping_option</span>\" => \"<span class=sf-dump-str>3</span>\"\n        \"<span class=sf-dump-key>shipping_amount</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"\n        \"<span class=sf-dump-key>shipping</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>default</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Local Pickup</span>\"\n              \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"\n            </samp>]\n            <span class=sf-dump-key>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Flat Rate</span>\"\n              \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">20.00</span>\"\n            </samp>]\n          </samp>]\n        </samp>]\n        \"<span class=sf-dump-key>default_shipping_method</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n        \"<span class=sf-dump-key>default_shipping_option</span>\" => \"<span class=sf-dump-str>3</span>\"\n        \"<span class=sf-dump-key>is_available_shipping</span>\" => <span class=sf-dump-const>true</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Fae Koelpin</span>\"\n    \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n    \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"12 characters\">+13198941196</span>\"\n    \"<span class=sf-dump-key>address</span>\" => \"<span class=sf-dump-str title=\"17 characters\">2358 Jadon Stream</span>\"\n    \"<span class=sf-dump-key>country</span>\" => \"<span class=sf-dump-str title=\"2 characters\">PE</span>\"\n    \"<span class=sf-dump-key>state</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Utah</span>\"\n    \"<span class=sf-dump-key>city</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Bashirianfurt</span>\"\n    \"<span class=sf-dump-key>zip_code</span>\" => \"<span class=sf-dump-str title=\"10 characters\">09306-7143</span>\"\n    \"<span class=sf-dump-key>shipping_method</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Botble\\Ecommerce\\Enums\\ShippingMethodEnum\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Botble\\Ecommerce\\Enums</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ShippingMethodEnum</span></span> {<a class=sf-dump-ref>#4655</a><samp data-depth=3 class=sf-dump-compact>\n      #<span class=sf-dump-protected title=\"Protected property\">value</span>: \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n    </samp>}\n    \"<span class=sf-dump-key>shipping_option</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>shipping_amount</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"\n    \"<span class=sf-dump-key>address_id</span>\" => <span class=sf-dump-num>3</span>\n    \"<span class=sf-dump-key>is_new_address</span>\" => <span class=sf-dump-const>false</span>\n  </samp>]\n  \"<span class=sf-dump-key>selected_payment_method</span>\" => \"<span class=sf-dump-str title=\"8 characters\">hyperpay</span>\"\n  \"<span class=sf-dump-key>order_id</span>\" => <span class=sf-dump-num>53</span>\n  \"<span class=sf-dump-key>hyperpay_checkout_id</span>\" => \"<span class=sf-dump-str title=\"46 characters\">460F3D223F84416536B101539705D3A8.uat01-vm-tx02</span>\"\n  \"<span class=sf-dump-key>hyperpay_payment_type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">visa</span>\"\n  \"<span class=sf-dump-key>hyperpay_order_id</span>\" => <span class=sf-dump-num>53</span>\n  \"<span class=sf-dump-key>hyperpay_amount</span>\" => <span class=sf-dump-num>528.0</span>\n  \"<span class=sf-dump-key>hyperpay_currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">SAR</span>\"\n  \"<span class=sf-dump-key>hyperpay_customer_id</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>hyperpay_customer_type</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Botble\\Ecommerce\\Models\\Customer</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>error_msg</span>\" => \"<span class=sf-dump-str title=\"64 characters\">Payment failed: invalid or missing parameter (Code: 200.300.404)</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-68249573\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "https://martfury.gc/payment/hyperpay/callback?id=460F3D223F84416536B101539705D3A8.uat01-vm-tx02&reso...", "action_name": "payments.hyperpay.callback", "controller_action": "Botble\\HyperPay\\Http\\Controllers\\HyperPayController@callback"}, "badge": "302 Found"}}