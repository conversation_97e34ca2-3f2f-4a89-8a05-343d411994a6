<?php

namespace Bo<PERSON>ble\HyperPay\Services\Abstracts;

use Botble\Payment\Services\Traits\PaymentErrorTrait;
use Bo<PERSON>ble\HyperPay\Services\HyperPayApiService;
use Exception;
use Illuminate\Support\Facades\Log;

abstract class HyperPayPaymentAbstract
{
    use PaymentErrorTrait;

    protected float $amount;
    protected string $currency;
    protected string $checkoutId;
    protected bool $supportRefundOnline = false;
    protected array $supportedCurrencies = ['SAR', 'AED', 'USD', 'EUR'];

    public function getSupportRefundOnline(): bool
    {
        return $this->supportRefundOnline;
    }

    public function supportedCurrencyCodes(): array
    {
        return $this->supportedCurrencies;
    }

    public function execute(array $data): ?string
    {
        try {
            return $this->makePayment($data);
        } catch (Exception $exception) {
            $this->setErrorMessageAndLogging($exception, 1);
            return null;
        }
    }

    abstract public function makePayment(array $data): ?string;

    abstract public function afterMakePayment(array $data): ?string;

    public function getApiUrl(): string
    {
        $isSandbox = get_payment_setting('sandbox_mode', HYPERPAY_PAYMENT_METHOD_NAME, false);

        return $isSandbox
            ? 'https://eu-test.oppwa.com/v1'
            : 'https://eu-prod.oppwa.com/v1';
    }

    public function getAccessToken(): ?string
    {
        return get_payment_setting('access_token', HYPERPAY_PAYMENT_METHOD_NAME);
    }

    public function getEntityId(string $paymentType = 'visa'): ?string
    {
        $entityIdKey = match($paymentType) {
            'visa', 'master' => 'visa_entity_id',
            'mada' => 'mada_entity_id',
            'amex' => 'amex_entity_id',
            'applepay' => 'applepay_entity_id',
            default => 'visa_entity_id'
        };

        return get_payment_setting($entityIdKey, HYPERPAY_PAYMENT_METHOD_NAME);
    }

    public function createCheckout(array $data): array
    {
        $apiService = app(HyperPayApiService::class);

        // Create a mock user object if needed
        $user = null;
        if (!empty($data['customer_email']) || !empty($data['customer_name'])) {
            $user = new class {
                public $email;
                public $name;

                public function __construct($email = null, $name = null) {
                    $this->email = $email;
                    $this->name = $name;
                }
            };
            $user->email = $data['customer_email'] ?? null;
            $user->name = $data['customer_name'] ?? null;
        }

        // Prepare trackable data
        $trackableData = [
            'amount' => $data['amount'],
            'order_id' => $data['order_id'] ?? uniqid('order_'),
            'payment_type' => $data['payment_type'] ?? 'visa',
            'customer_id' => $data['customer_id'] ?? null,
            'customer_type' => $data['customer_type'] ?? null,
        ];

        // Set redirect URL
        $apiService->addRedirectUrl(route('payments.hyperpay.callback'));

        // Create checkout using the new API service
        $result = $apiService->checkout(
            $trackableData,
            $user,
            $data['amount'],
            $data['payment_type'] ?? 'visa',
            request()
        );

        if (isset($result['error']) && $result['error']) {
            throw new Exception($result['message'] ?? 'Checkout creation failed');
        }

        if (isset($result['checkout_id'])) {
            return [
                'id' => $result['checkout_id'],
                'result' => [
                    'code' => '000.200.100',
                    'description' => 'Successfully created checkout'
                ]
            ];
        }

        throw new Exception('Failed to create checkout - no checkout ID returned');
    }

public function getPaymentStatus(string $resourcePath, string $checkoutId): array
{
    $apiService = app(HyperPayApiService::class);

    try {
        $result = $apiService->paymentStatus($resourcePath, $checkoutId);

        if (isset($result['error']) && $result['error']) {
            return $result;
        }

        // Convert the new API service response format to the expected format
        return [
            'id' => $result['transaction_id'] ?? $checkoutId,
            'result' => [
                'code' => $result['result_code'] ?? '999.999.999',
                'description' => $result['result_description'] ?? 'Unknown status'
            ],
            'status' => $result['status'] ?? 'FAILED'
        ];

    } catch (\Exception $e) {
        Log::error('HyperPay Payment Status Error', [
            'error' => $e->getMessage(),
            'checkout_id' => $checkoutId,
            'resource_path' => $resourcePath
        ]);

        return [
            'error' => true,
            'message' => $e->getMessage()
        ];
    }
}

    public function isSuccessfulPayment(string $resultCode): bool
    {
        // Success patterns from HyperPay documentation
        $successPatterns = [
            '/^(000\.000\.|000\.100\.1|000\.[36])/',  // Successful payments
        ];

        foreach ($successPatterns as $pattern) {
            if (preg_match($pattern, $resultCode)) {
                return true;
            }
        }

        return false;
    }

    public function isPendingPayment(string $resultCode): bool
    {
        // Pending patterns from HyperPay documentation
        $pendingPatterns = [
            '/^(000\.200)/',                          // Pending payments
            '/^(800\.400\.5|100\.400\.500)/',        // Pending v2 payments
        ];

        foreach ($pendingPatterns as $pattern) {
            if (preg_match($pattern, $resultCode)) {
                return true;
            }
        }

        return false;
    }

    public function setCurrency(string $currency): static
    {
        $this->currency = $currency;
        return $this;
    }

    public function setAmount(float $amount): static
    {
        $this->amount = $amount;
        return $this;
    }
}
