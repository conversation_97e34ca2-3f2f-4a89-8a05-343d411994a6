<?php

namespace Bo<PERSON>ble\HyperPay\Providers;

use Botble\Base\Traits\LoadAndPublishDataTrait;
use Botble\HyperPay\Services\HyperPayApiService;
use Illuminate\Support\ServiceProvider;

class HyperPayServiceProvider extends ServiceProvider
{
    use LoadAndPublishDataTrait;

    public function register(): void
    {
        // Register the HyperPay API service
        $this->app->singleton(HyperPayApiService::class, function () {
            return new HyperPayApiService();
        });
    }

    public function boot(): void
    {
        if (! is_plugin_active('payment')) {
            return;
        }

        $this->setNamespace('plugins/hyperpay')
            ->loadHelpers()
            ->loadRoutes()
            ->loadAndPublishTranslations()
            ->loadAndPublishViews()
            ->publishAssets();

        $this->app->register(HookServiceProvider::class);
    }
}
