{"__meta": {"id": "01K2QBS5HHMGE0X8QSYYQY5XH6", "datetime": "2025-08-15 17:17:31", "utime": **********.571107, "method": "GET", "uri": "/payment/hyperpay/callback?checkout_id=0ED2D1996DCA33C195FC346468A43449.uat01-vm-tx04&order_id=&id=0ED2D1996DCA33C195FC346468A43449.uat01-vm-tx04&resourcePath=%2Fv1%2Fcheckouts%2F0ED2D1996DCA33C195FC346468A43449.uat01-vm-tx04%2Fpayment", "ip": "127.0.0.1"}, "messages": {"count": 10, "messages": [{"message": "[17:17:26] LOG.info: HyperPay API Service Config Loaded {\n    \"gateway_url\": \"https:\\/\\/eu-test.oppwa.com\",\n    \"currency\": \"SAR\",\n    \"has_access_token\": true,\n    \"has_entity_id\": true,\n    \"sandbox_mode\": \"1\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.856144, "xdebug_link": null, "collector": "log"}, {"message": "[17:17:26] LOG.info: HyperPay Callback Received {\n    \"resource_path\": \"\\/v1\\/checkouts\\/0ED2D1996DCA33C195FC346468A43449.uat01-vm-tx04\\/payment\",\n    \"checkout_id\": \"0ED2D1996DCA33C195FC346468A43449.uat01-vm-tx04\",\n    \"all_params\": {\n        \"checkout_id\": \"0ED2D1996DCA33C195FC346468A43449.uat01-vm-tx04\",\n        \"order_id\": null,\n        \"id\": \"0ED2D1996DCA33C195FC346468A43449.uat01-vm-tx04\",\n        \"resourcePath\": \"\\/v1\\/checkouts\\/0ED2D1996DCA33C195FC346468A43449.uat01-vm-tx04\\/payment\"\n    },\n    \"session_data\": {\n        \"hyperpay_checkout_id\": \"0ED2D1996DCA33C195FC346468A43449.uat01-vm-tx04\",\n        \"hyperpay_order_id\": 54,\n        \"hyperpay_amount\": 1423.4,\n        \"hyperpay_currency\": \"SAR\",\n        \"hyperpay_payment_type\": \"visa\"\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.876288, "xdebug_link": null, "collector": "log"}, {"message": "[17:17:26] LOG.info: HyperPay HTTP Client GET Request {\n    \"url\": \"https:\\/\\/eu-test.oppwa.com\\/v1\\/checkouts\\/0ED2D1996DCA33C195FC346468A43449.uat01-vm-tx04\\/payment\",\n    \"parameters\": {\n        \"entityId\": \"8ac7a4c79483092601948366b9d1011b\"\n    },\n    \"access_token_present\": true\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.877814, "xdebug_link": null, "collector": "log"}, {"message": "[17:17:27] LOG.info: HyperPay HTTP Client GET Response {\n    \"status_code\": 200,\n    \"response_body\": \"{\\\"id\\\":\\\"8ac7a49f98adde210198aebc712f6ad2\\\",\\\"paymentType\\\":\\\"DB\\\",\\\"paymentBrand\\\":\\\"VISA\\\",\\\"amount\\\":\\\"1423.40\\\",\\\"currency\\\":\\\"SAR\\\",\\\"descriptor\\\":\\\"7287.1522.7470 General Test\\\",\\\"merchantTransactionId\\\":\\\"Jr8ffGqcyJ412uQEOevBZ8R3paYVWV2Dfbk8Yn8VavWNptax74ZmhbRqyINqeknk\\\",\\\"result\\\":{\\\"code\\\":\\\"000.100.110\\\",\\\"description\\\":\\\"Request successfully processed in 'Merchant in Integrator Test Mode'\\\"},\\\"resultDetails\\\":{\\\"ConnectorTxID1\\\":\\\"8ac7a49f98adde210198aebc712f6ad2\\\",\\\"clearingInstituteName\\\":\\\"NCB BANK\\\"},\\\"card\\\":{\\\"bin\\\":\\\"444000\\\",\\\"binCountry\\\":\\\"GB\\\",\\\"last4Digits\\\":\\\"0010\\\",\\\"holder\\\":\\\"Test\\\",\\\"expiryMonth\\\":\\\"12\\\",\\\"expiryYear\\\":\\\"2030\\\",\\\"issuer\\\":{\\\"bank\\\":\\\"CLYDESDALE BANK PLC\\\",\\\"website\\\":\\\"HTTP:\\/\\/WWW.CBONLINE.CO.UK\\/\\\",\\\"phone\\\":\\\"**********\\\"},\\\"type\\\":\\\"DEBIT\\\",\\\"level\\\":\\\"PREPAID ELECTRON\\\",\\\"country\\\":\\\"GB\\\",\\\"maxPanLength\\\":\\\"16\\\",\\\"binType\\\":\\\"PERSONAL\\\",\\\"regulatedFlag\\\":\\\"N\\\"},\\\"customer\\\":{\\\"givenName\\\":\\\"Fae\\\",\\\"surname\\\":\\\"Koelpin\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"ip\\\":\\\"**************\\\",\\\"ipCountry\\\":\\\"PK\\\"},\\\"threeDSecure\\\":{\\\"eci\\\":\\\"07\\\"},\\\"customParameters\\\":{\\\"SHOPPER_EndToEndIdentity\\\":\\\"046531db43d280a7623cc4796922556e5a525740805ebd3a2b84d610fda4e472\\\",\\\"CTPE_DESCRIPTOR_TEMPLATE\\\":\\\"\\\"},\\\"risk\\\":{\\\"score\\\":\\\"0\\\"},\\\"buildNumber\\\":\\\"8aa3728a6ba24ed3c4a6c6e9064ffd726f1bee67@2025-08-11 05:22:09 +0000\\\",\\\"timestamp\\\":\\\"2025-08-15 17:17:25+0000\\\",\\\"ndc\\\":\\\"0ED2D1996DCA33C195FC346468A43449.uat01-vm-tx04\\\"}\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.640119, "xdebug_link": null, "collector": "log"}, {"message": "[17:17:27] LOG.info: HyperPay HTTP Response - Payment Status {\n    \"status_code\": 200,\n    \"response_data\": {\n        \"id\": \"8ac7a49f98adde210198aebc712f6ad2\",\n        \"paymentType\": \"DB\",\n        \"paymentBrand\": \"VISA\",\n        \"amount\": \"1423.40\",\n        \"currency\": \"SAR\",\n        \"descriptor\": \"7287.1522.7470 General Test\",\n        \"merchantTransactionId\": \"Jr8ffGqcyJ412uQEOevBZ8R3paYVWV2Dfbk8Yn8VavWNptax74ZmhbRqyINqeknk\",\n        \"result\": {\n            \"code\": \"000.100.110\",\n            \"description\": \"Request successfully processed in 'Merchant in Integrator Test Mode'\"\n        },\n        \"resultDetails\": {\n            \"ConnectorTxID1\": \"8ac7a49f98adde210198aebc712f6ad2\",\n            \"clearingInstituteName\": \"NCB BANK\"\n        },\n        \"card\": {\n            \"bin\": \"444000\",\n            \"binCountry\": \"GB\",\n            \"last4Digits\": \"0010\",\n            \"holder\": \"Test\",\n            \"expiryMonth\": \"12\",\n            \"expiryYear\": \"2030\",\n            \"issuer\": {\n                \"bank\": \"CLYDESDALE BANK PLC\",\n                \"website\": \"HTTP:\\/\\/WWW.CBONLINE.CO.UK\\/\",\n                \"phone\": \"**********\"\n            },\n            \"type\": \"DEBIT\",\n            \"level\": \"PREPAID ELECTRON\",\n            \"country\": \"GB\",\n            \"maxPanLength\": \"16\",\n            \"binType\": \"PERSONAL\",\n            \"regulatedFlag\": \"N\"\n        },\n        \"customer\": {\n            \"givenName\": \"Fae\",\n            \"surname\": \"Koelpin\",\n            \"email\": \"<EMAIL>\",\n            \"ip\": \"**************\",\n            \"ipCountry\": \"PK\"\n        },\n        \"threeDSecure\": {\n            \"eci\": \"07\"\n        },\n        \"customParameters\": {\n            \"SHOPPER_EndToEndIdentity\": \"046531db43d280a7623cc4796922556e5a525740805ebd3a2b84d610fda4e472\",\n            \"CTPE_DESCRIPTOR_TEMPLATE\": \"\"\n        },\n        \"risk\": {\n            \"score\": \"0\"\n        },\n        \"buildNumber\": \"8aa3728a6ba24ed3c4a6c6e9064ffd726f1bee67@2025-08-11 05:22:09 +0000\",\n        \"timestamp\": \"2025-08-15 17:17:25+0000\",\n        \"ndc\": \"0ED2D1996DCA33C195FC346468A43449.uat01-vm-tx04\"\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.640892, "xdebug_link": null, "collector": "log"}, {"message": "[17:17:27] LOG.info: HyperPay Payment Result Analysis {\n    \"result_code\": \"000.100.110\",\n    \"result_description\": \"Request successfully processed in 'Merchant in Integrator Test Mode'\",\n    \"checkout_id\": \"0ED2D1996DCA33C195FC346468A43449.uat01-vm-tx04\",\n    \"charge_id\": \"8ac7a49f98adde210198aebc712f6ad2\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.64182, "xdebug_link": null, "collector": "log"}, {"message": "[17:17:27] LOG.info: HyperPay Processing Payment Hook {\n    \"order_id\": 54,\n    \"order_ids\": [\n        54\n    ],\n    \"amount\": 1423.4,\n    \"currency\": \"SAR\",\n    \"status\": \"completed\",\n    \"charge_id\": \"8ac7a49f98adde210198aebc712f6ad2\",\n    \"customer_id\": 2,\n    \"customer_type\": \"Botble\\\\Ecommerce\\\\Models\\\\Customer\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.642025, "xdebug_link": null, "collector": "log"}, {"message": "[17:17:31] LOG.debug: From: Example <<EMAIL>>\r\nTo: <EMAIL>\r\nSubject: New order(s) at MartFury\r\nMIME-Version: 1.0\r\nDate: Fri, 15 Aug 2025 17:17:31 +0000\r\nMessage-ID: <<EMAIL>>\r\nContent-Type: text/html; charset=utf-8\r\nContent-Transfer-Encoding: quoted-printable\r\n\r\n<!doctype html>\r\n<html lang=\"en\">\r\n\r\n<head>\r\n    <meta charset=\"UTF-8\">\r\n    <title>MartFury</title>\r\n</head>\r\n\r\n<body class=\"bb-bg-body\" dir=\"ltr\" style=\"margin: 0; padding: 0; font-size: 14px; line-height: 171.4285714286%; mso-line-height-rule: exactly; color: #3A4859; width: 100%; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale; -webkit-font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11'; font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11'; font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; background-color: #f6f7f9;\">\r\n\r\n<center>\r\n    <table class=\"bb-main bb-bg-body\" width=\"100%\" cellspacing=\"0\" cellpadding=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; background-color: #f6f7f9;\">\r\n        <tbody>\r\n            <tr>\r\n                <td align=\"center\" valign=\"top\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;\">\r\n                    <table class=\"bb-wrap\" cellspacing=\"0\" cellpadding=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; max-width: 640px; text-align: left; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\r\n                        <tbody>\r\n                            <tr>\r\n                                <td class=\"bb-p-sm\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 8px;\">\r\n                                    <table cellpadding=\"0\" cellspacing=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\r\n                                        <tbody>\r\n                                            <tr>\r\n                                                <td class=\"bb-py-lg\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding-top: 24px; padding-bottom: 24px;\">\r\n                                                    <table cellspacing=\"0\" cellpadding=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\r\n                                                        <tbody>\r\n                                                            <tr>\r\n                                                                <td class=\"bb-text-left\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: left;\">\r\n                                                                    <a href=\"https://martfury.gc\" style=\"color: #206bc4; text-decoration: none;\">\r\n                                                                        <img class=\"bb-logo\" src=\"https://martfury.gc/storage/general/logo.png\" alt=\"MartFury\" style=\"line-height: 100%; outline: none; text-decoration: none; vertical-align: baseline; font-size: 0; border: 0 none; max-height: 40px;\">\r\n                                                                    </a>\r\n                                                                </td>\r\n                                                                <td class=\"bb-text-right\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right;\">\r\n                                                                    2025-08-15 17:17:31\r\n                                                                </td>\r\n                                                            </tr>\r\n                                                        </tbody>\r\n                                                    </table>\r\n                                                </td>\r\n                                            </tr>\r\n                                        </tbody>\r\n                                    </table>\r\n\r\n\r\n<div class=\"bb-main-content\">\r\n    <table class=\"bb-box\" cellpadding=\"0\" cellspacing=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; background: #ffffff; border-radius: 4px; -webkit-box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05); box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05); border: 1px solid #dce0e5; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\r\n        <tbody>\r\n            <tr>\r\n                <td class=\"bb-content bb-pb-0\" align=\"center\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 40px 48px; padding-bottom: 0;\">\r\n                    <table class=\"bb-icon bb-icon-lg bb-bg-blue\" cellspacing=\"0\" cellpadding=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 0; border-radius: 50%; background: #edeef0; line-height: 100%; font-weight: 300; border-collapse: separate; text-align: center; width: 72px; height: 72px; font-size: 48px; background-color: #206bc4; color: #ffffff; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\r\n                        <tbody>\r\n                            <tr>\r\n                                <td valign=\"middle\" align=\"center\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;\">\r\n                                    <img src=\"https://martfury.gc/vendor/core/core/base/images/email-icons/shopping-cart.png\" class=\"bb-va-middle\" width=\"40\" height=\"40\" alt=\"Icon\" style=\"border: 0 none; line-height: 100%; outline: none; text-decoration: none; font-size: 0; vertical-align: middle; display: block; width: 40px; height: 40px;\">\r\n                                </td>\r\n                            </tr>\r\n                        </tbody>\r\n                    </table>\r\n                    <h1 class=\"bb-text-center bb-m-0 bb-mt-md\" style=\"font-weight: 600; color: #232b42; font-size: 28px; line-height: 130%; text-align: center; margin: 0; margin-top: 16px;\">Order successfully!</h1>\r\n                </td>\r\n            </tr>\r\n            <tr>\r\n                <td class=\"bb-content\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 40px 48px;\">\r\n                    <div>Dear Global Office,</div>\r\n                    <div>You got a new order on MartFury!</div>\r\n                </td>\r\n            </tr>\r\n            <tr>\r\n                <td class=\"bb-content bb-pt-0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 40px 48px; padding-top: 0;\">\r\n                    <table class=\"bb-row bb-mb-md\" cellpadding=\"0\" cellspacing=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; table-layout: fixed; margin-bottom: 16px; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\r\n                        <tbody>\r\n                            <tr>\r\n                                <td class=\"bb-bb-col\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;\">\r\n                                    <h4 class=\"bb-m-0\" style=\"font-weight: 600; color: #232b42; font-size: 16px; margin: 0;\">Customer Information</h4>\r\n                                    <div>Name: <strong style=\"font-weight: 600;\">Fae Koelpin</strong>\r\n</div>\r\n                                                                        <div>Phone: <strong style=\"font-weight: 600;\">+18598017656</strong>\r\n</div>\r\n                                                                                                                                                <div>Address: <strong style=\"font-weight: 600;\">2358 Jadon Stream, Bashirianfurt, Utah, PE</strong>\r\n</div>\r\n                                                                    </td>\r\n                            </tr>\r\n                        </tbody>\r\n                    </table>\r\n                </td>\r\n            </tr>\r\n            <tr>\r\n                <td class=\"bb-content bb-pt-0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 40px 48px; padding-top: 0;\">\r\n                    <h4 style=\"font-weight: 600; margin: 0 0 0.5em; color: #232b42; font-size: 16px;\">Here's what you ordered:</h4>\r\n                    <a class=\"button button-blue\" href=\"https://martfury.gc/orders/tracking?order_id=%2310000054&amp;email=vendor%40botble.com\" style=\"color: #206bc4; text-decoration: none;\">View order</a>\r\n    or <a href=\"https://martfury.gc\" style=\"color: #206bc4; text-decoration: none;\">Go to our shop</a>\r\n\r\n    <br>\r\n\r\n<table class=\"bb-table\" cellspacing=\"0\" cellpadding=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\r\n    <thead>\r\n        <tr>\r\n            <th colspan=\"2\" style=\"text-transform: uppercase; font-weight: 600; color: #667382; font-size: 12px; padding: 0 0 4px 0; padding-left: 0;\"></th>\r\n            <th style=\"text-transform: uppercase; font-weight: 600; color: #667382; font-size: 12px; padding: 0 0 4px 0;\">Quantity</th>\r\n            <th class=\"bb-text-right\" style=\"text-align: right; text-transform: uppercase; font-weight: 600; color: #667382; font-size: 12px; padding: 0 0 4px 0; padding-right: 0;\">Price</th>\r\n        </tr>\r\n    </thead>\r\n\r\n    <tbody>\r\n                <tr>\r\n            <td class=\"bb-pr-0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding-right: 0; padding: 4px 0; padding-left: 0;\">\r\n                <a href=\"\" style=\"color: #206bc4; text-decoration: none;\">\r\n                    <img src=\"https://martfury.gc/storage/products/10-150x150.jpg\" class=\" bb-rounded\" width=\"64\" height=\"64\" alt=\"\" style=\"line-height: 100%; outline: none; text-decoration: none; vertical-align: baseline; font-size: 0; border: 0 none; border-radius: 4px;\">\r\n                </a>\r\n            </td>\r\n            <td class=\"bb-pl-md bb-w-100p\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; width: 100%; padding-left: 16px !important; padding: 4px 0;\">\r\n                <strong style=\"font-weight: 600;\">Herschel Leather Duffle Bag In Brown Color</strong><br>\r\n                                    <span class=\"bb-text-muted\" style=\"color: #667382;\">(Color: Blue, Size: L)</span>\r\n                \r\n                            </td>\r\n            <td class=\"bb-text-center\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: center; padding: 4px 0;\">x 1</td>\r\n            <td class=\"bb-text-right\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right; padding: 4px 0; padding-right: 0;\">SAR1,294.00</td>\r\n        </tr>\r\n    \r\n                                    <tr>\r\n                    <td colspan=\"2\" class=\"bb-border-top bb-text-right\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right; border-top: 1px solid #dce0e5; padding: 4px 0; padding-left: 0;\">Subtotal</td>\r\n                    <td colspan=\"2\" class=\"bb-border-top bb-text-right\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right; border-top: 1px solid #dce0e5; padding: 4px 0; padding-right: 0;\">SAR1,294.00</td>\r\n                </tr>\r\n            \r\n            \r\n                            <tr>\r\n                    <td colspan=\"2\" class=\"bb-text-right\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right; padding: 4px 0; padding-left: 0;\">Tax</td>\r\n                    <td colspan=\"2\" class=\"bb-text-right\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right; padding: 4px 0; padding-right: 0;\">SAR129.40</td>\r\n                </tr>\r\n            \r\n            \r\n                        <tr>\r\n                <td colspan=\"2\" class=\"bb-text-right bb-font-strong bb-h3 bb-m-0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; color: #232b42; font-size: 20px; line-height: 130%; text-align: right; font-weight: 600; margin: 0; padding: 4px 0; padding-left: 0;\">Total</td>\r\n                <td colspan=\"2\" class=\"bb-font-strong bb-h3 bb-m-0 bb-text-right\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; color: #232b42; font-size: 20px; line-height: 130%; text-align: right; font-weight: 600; margin: 0; padding: 4px 0; padding-right: 0;\">SAR1,423.40</td>\r\n            </tr>\r\n            </tbody>\r\n</table>\r\n\r\n\r\n                                    </td>\r\n            </tr>\r\n            <tr>\r\n                <td class=\"bb-content bb-border-top\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 40px 48px; border-top: 1px solid #dce0e5;\">\r\n                    <table class=\"bb-row bb-mb-md\" cellpadding=\"0\" cellspacing=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; table-layout: fixed; margin-bottom: 16px; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\r\n                        <tbody>\r\n                            <tr>\r\n                                <td class=\"bb-bb-col\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;\">\r\n                                    <h4 class=\"bb-m-0\" style=\"font-weight: 600; color: #232b42; font-size: 16px; margin: 0;\">Order number</h4>\r\n                                    <div>#10000054</div>\r\n                                </td>\r\n                                <td class=\"bb-col-spacer\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; vertical-align: top; width: 24px;\"></td>\r\n                                <td class=\"bb-col\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; vertical-align: top;\">\r\n                                    <h4 class=\"bb-mb-0\" style=\"font-weight: 600; margin: 0 0 0.5em; color: #232b42; font-size: 16px; margin-bottom: 0;\">Order date</h4>\r\n                                    <div>2025-08-15 17:14:38</div>\r\n                                </td>\r\n                            </tr>\r\n                        </tbody>\r\n                    </table>\r\n                    <table class=\"bb-row\" cellpadding=\"0\" cellspacing=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; table-layout: fixed; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\r\n                        <tbody>\r\n                            <tr>\r\n                                <td class=\"bb-col\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; vertical-align: top;\">\r\n                                                                        <h4 class=\"bb-m-0\" style=\"font-weight: 600; color: #232b42; font-size: 16px; margin: 0;\">Shipping Method</h4>\r\n                                    <div>\r\n                                        Free delivery\r\n                                    </div>\r\n                                                                    </td>\r\n\r\n                                <td class=\"bb-col-spacer\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; vertical-align: top; width: 24px;\"></td>\r\n                                <td class=\"bb-col\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; vertical-align: top;\">\r\n                                    <h4 class=\"bb-m-0\" style=\"font-weight: 600; color: #232b42; font-size: 16px; margin: 0;\">Payment Method</h4>\r\n                                    <div>\r\n                                        HyperPay\r\n                                    </div>\r\n                                </td>\r\n                            </tr>\r\n                        </tbody>\r\n                    </table>\r\n                </td>\r\n            </tr>\r\n        </tbody>\r\n    </table>\r\n</div>\r\n\r\n<table cellspacing=\"0\" cellpadding=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\r\n    <tbody>\r\n        <tr>\r\n            <td class=\"bb-py-xl\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding-top: 48px; padding-bottom: 48px;\">\r\n                <table class=\"bb-text-center bb-text-muted\" cellspacing=\"0\" cellpadding=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; color: #667382; text-align: center; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\r\n                    <tbody>\r\n                    \r\n                    <tr>\r\n                        <td class=\"bb-px-lg\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding-right: 24px; padding-left: 24px;\">\r\n                            © 2025 MartFury. All Rights Reserved.\r\n                        </td>\r\n                    </tr>\r\n\r\n                                            <tr>\r\n                            <td class=\"bb-pt-md\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding-top: 16px;\">\r\n                                If you have any questions, feel free to message us at <a href=\"mailto:<EMAIL>\" style=\"color: #206bc4; text-decoration: none;\"><EMAIL></a>.\r\n                            </td>\r\n                        </tr>\r\n                                        </tbody>\r\n                </table>\r\n            </td>\r\n        </tr>\r\n    </tbody>\r\n</table>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table>\r\n</center>\r\n</body>\r\n\r\n</html>", "message_html": null, "is_string": false, "label": "debug", "time": **********.522979, "xdebug_link": null, "collector": "log"}, {"message": "[17:17:31] LOG.info: HyperPay Checkout Token Retrieved {\n    \"order_id\": 54,\n    \"checkout_token\": \"542d9a1b1e7195f913e91bc042defb6e\",\n    \"payment_result\": \"8ac7a49f98adde210198aebc712f6ad2\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.559186, "xdebug_link": null, "collector": "log"}, {"message": "[17:17:31] LOG.info: HyperPay Payment Success {\n    \"charge_id\": \"8ac7a49f98adde210198aebc712f6ad2\",\n    \"order_id\": 54,\n    \"checkout_token\": \"542d9a1b1e7195f913e91bc042defb6e\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.559578, "xdebug_link": null, "collector": "log"}]}, "time": {"count": 9, "start": **********.051763, "end": **********.571151, "duration": 5.51938796043396, "duration_str": "5.52s", "measures": [{"label": "Booting", "start": **********.051763, "relative_start": 0, "end": **********.798212, "relative_end": **********.798212, "duration": 0.****************, "duration_str": "746ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.798227, "relative_start": 0.****************, "end": **********.571154, "relative_end": 3.0994415283203125e-06, "duration": 4.****************, "duration_str": "4.77s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.823394, "relative_start": 0.****************, "end": **********.839973, "relative_end": **********.839973, "duration": 0.*****************, "duration_str": "16.58ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: plugins/marketplace::emails.partials.order-detail", "start": **********.081567, "relative_start": 5.***************, "end": **********.081567, "relative_end": **********.081567, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::emails.partials.order-detail", "start": **********.082804, "relative_start": 5.***************, "end": **********.082804, "relative_end": **********.082804, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::emails.partials.order-detail", "start": **********.108665, "relative_start": 5.056901931762695, "end": **********.108665, "relative_end": **********.108665, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::emails.partials.order-delivery-notes", "start": **********.115266, "relative_start": 5.06350302696228, "end": **********.115266, "relative_end": **********.115266, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "mail: New order(s) at MartFury", "start": **********.518228, "relative_start": 5.466464996337891, "end": **********.523542, "relative_end": **********.523542, "duration": 0.005313873291015625, "duration_str": "5.31ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "mail", "group": null}, {"label": "Preparing Response", "start": **********.564167, "relative_start": 5.512403964996338, "end": **********.565092, "relative_end": **********.565092, "duration": 0.0009250640869140625, "duration_str": "925μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 73197120, "peak_usage_str": "70MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.21.0", "PHP Version": "8.3.17", "Environment": "local", "Debug Mode": "Enabled", "URL": "martfury.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 4, "nb_templates": 4, "templates": [{"name": "plugins/marketplace::emails.partials.order-detail", "param_count": null, "params": [], "start": **********.08154, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/plugins/marketplace/resources/views/emails/partials/order-detail.blade.phpplugins/marketplace::emails.partials.order-detail", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fmarketplace%2Fresources%2Fviews%2Femails%2Fpartials%2Forder-detail.blade.php&line=1", "ajax": false, "filename": "order-detail.blade.php", "line": "?"}}, {"name": "plugins/ecommerce::emails.partials.order-detail", "param_count": null, "params": [], "start": **********.082784, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/plugins/ecommerce/resources/views/emails/partials/order-detail.blade.phpplugins/ecommerce::emails.partials.order-detail", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Femails%2Fpartials%2Forder-detail.blade.php&line=1", "ajax": false, "filename": "order-detail.blade.php", "line": "?"}}, {"name": "plugins/ecommerce::emails.partials.order-detail", "param_count": null, "params": [], "start": **********.108641, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/plugins/ecommerce/resources/views/emails/partials/order-detail.blade.phpplugins/ecommerce::emails.partials.order-detail", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Femails%2Fpartials%2Forder-detail.blade.php&line=1", "ajax": false, "filename": "order-detail.blade.php", "line": "?"}}, {"name": "plugins/ecommerce::emails.partials.order-delivery-notes", "param_count": null, "params": [], "start": **********.115241, "type": "blade", "hash": "bladeD:\\laragon\\www\\martfury\\platform/plugins/ecommerce/resources/views/emails/partials/order-delivery-notes.blade.phpplugins/ecommerce::emails.partials.order-delivery-notes", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Femails%2Fpartials%2Forder-delivery-notes.blade.php&line=1", "ajax": false, "filename": "order-delivery-notes.blade.php", "line": "?"}}]}, "queries": {"count": 68, "nb_statements": 68, "nb_visible_statements": 68, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.12580000000000002, "accumulated_duration_str": "126ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Footprints/TrackingFilter.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Footprints\\TrackingFilter.php", "line": 45}], "start": **********.871244, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 0, "width_percent": 0.397}, {"sql": "select * from `ec_orders` where `id` in (54)", "type": "query", "params": [], "bindings": [54], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 309}, {"index": 20, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 46}, {"index": 22, "namespace": null, "name": "platform/plugins/hyperpay/src/Http/Controllers/HyperPayController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\hyperpay\\src\\Http\\Controllers\\HyperPayController.php", "line": 84}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.6425, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 0.397, "width_percent": 0.421}, {"sql": "select * from `ec_currencies` where `is_default` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/CurrencySupport.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\CurrencySupport.php", "line": 74}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 311}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 46}, {"index": 24, "namespace": null, "name": "platform/plugins/hyperpay/src/Http/Controllers/HyperPayController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\hyperpay\\src\\Http\\Controllers\\HyperPayController.php", "line": 84}], "start": **********.6460378, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 0.819, "width_percent": 0.318}, {"sql": "select * from `payments` where `charge_id` = '8ac7a49f98adde210198aebc712f6ad2' and `order_id` in (54) limit 1", "type": "query", "params": [], "bindings": ["8ac7a49f98adde210198aebc712f6ad2", 54], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/payment/src/Supports/PaymentHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\payment\\src\\Supports\\PaymentHelper.php", "line": 39}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 318}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 46}, {"index": 24, "namespace": null, "name": "platform/plugins/hyperpay/src/Http/Controllers/HyperPayController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\hyperpay\\src\\Http\\Controllers\\HyperPayController.php", "line": 84}], "start": **********.648388, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 1.137, "width_percent": 0.827}, {"sql": "insert into `payments` (`amount`, `payment_fee`, `currency`, `charge_id`, `order_id`, `customer_id`, `customer_type`, `payment_channel`, `status`, `updated_at`, `created_at`) values ('1423.40', 0, 'SAR', '8ac7a49f98adde210198aebc712f6ad2', 54, 2, 'Botble\\\\Ecommerce\\\\Models\\\\Customer', 'hyperpay', 'completed', '2025-08-15 17:17:27', '2025-08-15 17:17:27')", "type": "query", "params": [], "bindings": ["1423.40", 0, "SAR", "8ac7a49f98adde210198aebc712f6ad2", 54, 2, "Botble\\Ecommerce\\Models\\Customer", {"value": "hyperpay", "label": "HyperPay"}, {"value": "completed", "label": "Completed"}, "2025-08-15 17:17:27", "2025-08-15 17:17:27"], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "platform/plugins/payment/src/Supports/PaymentHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\payment\\src\\Supports\\PaymentHelper.php", "line": 60}, {"index": 19, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 318}, {"index": 23, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 46}, {"index": 25, "namespace": null, "name": "platform/plugins/hyperpay/src/Http/Controllers/HyperPayController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\hyperpay\\src\\Http\\Controllers\\HyperPayController.php", "line": 84}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.654349, "duration": 0.0039700000000000004, "duration_str": "3.97ms", "memory": 0, "memory_str": null, "filename": "PaymentHelper.php:60", "source": {"index": 18, "namespace": null, "name": "platform/plugins/payment/src/Supports/PaymentHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\payment\\src\\Supports\\PaymentHelper.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fpayment%2Fsrc%2FSupports%2FPaymentHelper.php&line=60", "ajax": false, "filename": "PaymentHelper.php", "line": "60"}, "connection": "martfury", "explain": null, "start_percent": 1.963, "width_percent": 3.156}, {"sql": "select * from `ec_orders` where `id` in (54)", "type": "query", "params": [], "bindings": [54], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 73}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 321}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 46}, {"index": 24, "namespace": null, "name": "platform/plugins/hyperpay/src/Http/Controllers/HyperPayController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\hyperpay\\src\\Http\\Controllers\\HyperPayController.php", "line": 84}], "start": **********.6647441, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 5.119, "width_percent": 0.382}, {"sql": "select * from `payments` where `charge_id` = '8ac7a49f98adde210198aebc712f6ad2' and `order_id` in (54)", "type": "query", "params": [], "bindings": ["8ac7a49f98adde210198aebc712f6ad2", 54], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 83}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 321}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 46}, {"index": 24, "namespace": null, "name": "platform/plugins/hyperpay/src/Http/Controllers/HyperPayController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\hyperpay\\src\\Http\\Controllers\\HyperPayController.php", "line": 84}], "start": **********.667348, "duration": 0.004030000000000001, "duration_str": "4.03ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 5.501, "width_percent": 3.203}, {"sql": "update `ec_orders` set `payment_id` = 95, `ec_orders`.`updated_at` = '2025-08-15 17:17:27' where `id` = 54", "type": "query", "params": [], "bindings": [95, "2025-08-15 17:17:27", 54], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 90}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 321}, {"index": 20, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 46}, {"index": 22, "namespace": null, "name": "platform/plugins/hyperpay/src/Http/Controllers/HyperPayController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\hyperpay\\src\\Http\\Controllers\\HyperPayController.php", "line": 84}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.67406, "duration": 0.00404, "duration_str": "4.04ms", "memory": 0, "memory_str": null, "filename": "OrderHelper.php:90", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 90}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FSupports%2FOrderHelper.php&line=90", "ajax": false, "filename": "OrderHelper.php", "line": "90"}, "connection": "martfury", "explain": null, "start_percent": 8.704, "width_percent": 3.211}, {"sql": "select * from `ec_orders` where `ec_orders`.`id` = 54 limit 1", "type": "query", "params": [], "bindings": [54], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 110}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 63}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesModels.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesModels.php", "line": 97}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php", "line": 62}], "start": **********.7083728, "duration": 0.00409, "duration_str": "4.09ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 11.916, "width_percent": 3.251}, {"sql": "select exists(select * from `ec_invoices` where `ec_invoices`.`reference_id` = 54 and `ec_invoices`.`reference_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [54], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/InvoiceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\InvoiceHelper.php", "line": 31}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/GenerateInvoiceListener.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\GenerateInvoiceListener.php", "line": 16}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}], "start": **********.718944, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "InvoiceHelper.php:31", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/InvoiceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\InvoiceHelper.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FSupports%2FInvoiceHelper.php&line=31", "ajax": false, "filename": "InvoiceHelper.php", "line": "31"}, "connection": "martfury", "explain": null, "start_percent": 15.167, "width_percent": 0.66}, {"sql": "select * from `ec_order_addresses` where `ec_order_addresses`.`order_id` = 54 and `ec_order_addresses`.`order_id` is not null and `type` = 'shipping_address' limit 1", "type": "query", "params": [], "bindings": [54, "shipping_address"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/InvoiceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\InvoiceHelper.php", "line": 35}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/GenerateInvoiceListener.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\GenerateInvoiceListener.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.721571, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 15.827, "width_percent": 0.485}, {"sql": "select * from `ec_order_tax_information` where `ec_order_tax_information`.`order_id` = 54 and `ec_order_tax_information`.`order_id` is not null limit 1", "type": "query", "params": [], "bindings": [54], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 20, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/InvoiceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\InvoiceHelper.php", "line": 41}, {"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/GenerateInvoiceListener.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\GenerateInvoiceListener.php", "line": 16}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.7235808, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 16.312, "width_percent": 0.469}, {"sql": "select exists(select * from `countries`) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 597}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Traits/LocationTrait.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Traits\\LocationTrait.php", "line": 74}, {"index": 19, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 20, "namespace": null, "name": "platform/plugins/ecommerce/src/Traits/LocationTrait.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Traits\\LocationTrait.php", "line": 94}, {"index": 27, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.725589, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "EcommerceHelper.php:597", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 597}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FSupports%2FEcommerceHelper.php&line=597", "ajax": false, "filename": "EcommerceHelper.php", "line": "597"}, "connection": "martfury", "explain": null, "start_percent": 16.781, "width_percent": 0.223}, {"sql": "select * from `payments` where `payments`.`id` = 95 limit 1", "type": "query", "params": [], "bindings": [95], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/InvoiceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\InvoiceHelper.php", "line": 76}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/GenerateInvoiceListener.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\GenerateInvoiceListener.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.759335, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 17.003, "width_percent": 0.874}, {"sql": "select max(`id`) as aggregate from `ec_invoices`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Invoice.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Invoice.php", "line": 84}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Invoice.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Invoice.php", "line": 58}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/InvoiceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\InvoiceHelper.php", "line": 86}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/GenerateInvoiceListener.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\GenerateInvoiceListener.php", "line": 16}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.767164, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "Invoice.php:84", "source": {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Invoice.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Invoice.php", "line": 84}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FInvoice.php&line=84", "ajax": false, "filename": "Invoice.php", "line": "84"}, "connection": "martfury", "explain": null, "start_percent": 17.878, "width_percent": 0.819}, {"sql": "select exists(select * from `ec_invoices` where `code` = 'INV-45') as `exists`", "type": "query", "params": [], "bindings": ["INV-45"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Invoice.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Invoice.php", "line": 89}, {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Invoice.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Invoice.php", "line": 58}, {"index": 20, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/InvoiceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\InvoiceHelper.php", "line": 86}, {"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/GenerateInvoiceListener.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\GenerateInvoiceListener.php", "line": 16}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.769085, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "Invoice.php:89", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Invoice.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Invoice.php", "line": 89}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FInvoice.php&line=89", "ajax": false, "filename": "Invoice.php", "line": "89"}, "connection": "martfury", "explain": null, "start_percent": 18.696, "width_percent": 0.715}, {"sql": "insert into `ec_invoices` (`reference_id`, `reference_type`, `company_name`, `company_logo`, `customer_name`, `customer_email`, `customer_phone`, `customer_address`, `customer_tax_id`, `payment_id`, `status`, `paid_at`, `tax_amount`, `shipping_amount`, `payment_fee`, `discount_amount`, `sub_total`, `amount`, `shipping_method`, `shipping_option`, `coupon_code`, `discount_description`, `description`, `created_at`, `code`, `updated_at`) values (54, 'Botble\\\\Ecommerce\\\\Models\\\\Order', '', null, 'Fae Koelpin', '<EMAIL>', '+13198941196', '2358 Jadon Stream, Bashirianfurt, Utah, PE', null, 95, 'completed', '2025-08-15 17:17:27', '129.40', '0.00', '0.00', '0.00', '1294.00', '1423.40', 'default', '1', null, null, null, '2025-08-15 17:14:38', 'INV-45', '2025-08-15 17:17:27')", "type": "query", "params": [], "bindings": [54, "Botble\\Ecommerce\\Models\\Order", "", null, "<PERSON><PERSON>", "<EMAIL>", "+13198941196", "2358 Jadon Stream, Bashirianfurt, Utah, PE", null, 95, {"value": "completed", "label": "Completed"}, "2025-08-15 17:17:27", "129.40", "0.00", "0.00", "0.00", "1294.00", "1423.40", {"value": "default", "label": "<PERSON><PERSON><PERSON>"}, "1", null, null, null, "2025-08-15 17:14:38", "INV-45", "2025-08-15 17:17:27"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/InvoiceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\InvoiceHelper.php", "line": 86}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/GenerateInvoiceListener.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\GenerateInvoiceListener.php", "line": 16}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}], "start": **********.771312, "duration": 0.0040999999999999995, "duration_str": "4.1ms", "memory": 0, "memory_str": null, "filename": "InvoiceHelper.php:86", "source": {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/InvoiceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\InvoiceHelper.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FSupports%2FInvoiceHelper.php&line=86", "ajax": false, "filename": "InvoiceHelper.php", "line": "86"}, "connection": "martfury", "explain": null, "start_percent": 19.412, "width_percent": 3.259}, {"sql": "select * from `ec_order_product` where `ec_order_product`.`order_id` = 54 and `ec_order_product`.`order_id` is not null", "type": "query", "params": [], "bindings": [54], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/InvoiceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\InvoiceHelper.php", "line": 88}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/GenerateInvoiceListener.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\GenerateInvoiceListener.php", "line": 16}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.778838, "duration": 0.00281, "duration_str": "2.81ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 22.671, "width_percent": 2.234}, {"sql": "select * from `ec_products` where `ec_products`.`id` in (43) and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published'))", "type": "query", "params": [], "bindings": [1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 27, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 28, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/InvoiceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\InvoiceHelper.php", "line": 88}, {"index": 30, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/GenerateInvoiceListener.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\GenerateInvoiceListener.php", "line": 16}], "start": **********.784448, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 24.905, "width_percent": 0.564}, {"sql": "insert into `ec_invoice_items` (`reference_id`, `reference_type`, `name`, `description`, `image`, `qty`, `price`, `sub_total`, `tax_amount`, `discount_amount`, `amount`, `options`, `invoice_id`, `updated_at`, `created_at`) values (43, 'Bo<PERSON>ble\\\\Ecommerce\\\\Models\\\\Product', '<PERSON><PERSON><PERSON> Leather Duffle Bag In Brown Color', null, 'products/10.jpg', 1, '1294.00', 1294, '129.40', 0, 1423.4, '{\\\"image\\\":\\\"products\\\\/10.jpg\\\",\\\"attributes\\\":\\\"(Color: Blue, Size: L)\\\",\\\"taxRate\\\":10,\\\"taxClasses\\\":{\\\"VAT\\\":10},\\\"options\\\":[],\\\"extras\\\":[],\\\"sku\\\":\\\"SW-145-A0\\\",\\\"weight\\\":574}', 45, '2025-08-15 17:17:27', '2025-08-15 17:17:27')", "type": "query", "params": [], "bindings": [43, "Botble\\Ecommerce\\Models\\Product", "<PERSON><PERSON><PERSON>g In Brown Color", null, "products/10.jpg", 1, "1294.00", 1294, "129.40", 0, 1423.4, "{\"image\":\"products\\/10.jpg\",\"attributes\":\"(Color: Blue, Size: L)\",\"taxRate\":10,\"taxClasses\":{\"VAT\":10},\"options\":[],\"extras\":[],\"sku\":\"SW-145-A0\",\"weight\":574}", 45, "2025-08-15 17:17:27", "2025-08-15 17:17:27"], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/InvoiceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\InvoiceHelper.php", "line": 89}, {"index": 20, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/GenerateInvoiceListener.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\GenerateInvoiceListener.php", "line": 16}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}], "start": **********.826264, "duration": 0.00425, "duration_str": "4.25ms", "memory": 0, "memory_str": null, "filename": "InvoiceHelper.php:89", "source": {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/InvoiceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\InvoiceHelper.php", "line": 89}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FSupports%2FInvoiceHelper.php&line=89", "ajax": false, "filename": "InvoiceHelper.php", "line": "89"}, "connection": "martfury", "explain": null, "start_percent": 25.469, "width_percent": 3.378}, {"sql": "select * from `payments` where `payments`.`id` = 95 limit 1", "type": "query", "params": [], "bindings": [95], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/InvoiceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\InvoiceHelper.php", "line": 191}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/InvoiceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\InvoiceHelper.php", "line": 125}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/InvoiceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\InvoiceHelper.php", "line": 146}], "start": **********.016307, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 28.847, "width_percent": 0.39}, {"sql": "select * from `ec_invoice_items` where `ec_invoice_items`.`invoice_id` in (45)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/InvoiceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\InvoiceHelper.php", "line": 232}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/InvoiceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\InvoiceHelper.php", "line": 125}, {"index": 26, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/InvoiceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\InvoiceHelper.php", "line": 146}, {"index": 28, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 959}], "start": **********.01889, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 29.237, "width_percent": 0.763}, {"sql": "select * from `ec_orders` where `ec_orders`.`id` in (54)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/InvoiceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\InvoiceHelper.php", "line": 232}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/InvoiceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\InvoiceHelper.php", "line": 125}, {"index": 26, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/InvoiceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\InvoiceHelper.php", "line": 146}, {"index": 28, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 959}], "start": **********.022109, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 30, "width_percent": 0.58}, {"sql": "select * from `languages` where `lang_is_default` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 940}, {"index": 19, "namespace": null, "name": "platform/plugins/language/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\language\\src\\Providers\\HookServiceProvider.php", "line": 89}, {"index": 23, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 25, "namespace": null, "name": "vendor/botble/platform/base/src/Supports/Language.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Supports\\Language.php", "line": 497}], "start": **********.0470712, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 30.58, "width_percent": 0.35}, {"sql": "select `lang_locale`, `lang_code`, `lang_name`, `lang_flag`, `lang_is_rtl` from `languages` order by `lang_order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 156}, {"index": 18, "namespace": null, "name": "platform/plugins/language/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\language\\src\\Providers\\HookServiceProvider.php", "line": 118}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 24, "namespace": null, "name": "vendor/botble/platform/base/src/Supports/Language.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Supports\\Language.php", "line": 499}], "start": **********.05128, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 30.93, "width_percent": 0.302}, {"sql": "select * from `ec_order_addresses` where `ec_order_addresses`.`order_id` = 54 and `ec_order_addresses`.`order_id` is not null and `type` = 'shipping_address' limit 1", "type": "query", "params": [], "bindings": [54, "shipping_address"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/InvoiceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\InvoiceHelper.php", "line": 345}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/InvoiceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\InvoiceHelper.php", "line": 125}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/InvoiceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\InvoiceHelper.php", "line": 146}], "start": **********.055542, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 31.232, "width_percent": 0.382}, {"sql": "select * from `mp_stores` where `mp_stores`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\HookServiceProvider.php", "line": 253}, {"index": 27, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 29, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/InvoiceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\InvoiceHelper.php", "line": 125}], "start": **********.060033, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 31.614, "width_percent": 0.493}, {"sql": "select * from `ec_currencies` order by `order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/CurrencySupport.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\CurrencySupport.php", "line": 107}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/CurrencySupport.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\CurrencySupport.php", "line": 41}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/helpers/currencies.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\helpers\\currencies.php", "line": 141}, {"index": 21, "namespace": null, "name": "f1d4fe725dee349e7b0bf732bc8c5346ff5bf0bb", "file": "f1d4fe725dee349e7b0bf732bc8c5346ff5bf0bb", "line": 150}], "start": **********.374763, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 32.107, "width_percent": 0.39}, {"sql": "select * from `ec_order_addresses` where `ec_order_addresses`.`order_id` = 54 and `ec_order_addresses`.`order_id` is not null and `type` = 'shipping_address' limit 1", "type": "query", "params": [], "bindings": [54, "shipping_address"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/OrderCreatedNotification.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\OrderCreatedNotification.php", "line": 18}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 508}, {"index": 30, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 321}], "start": **********.934298, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 32.496, "width_percent": 0.35}, {"sql": "select * from `ec_order_product` where `ec_order_product`.`order_id` = 54 and `ec_order_product`.`order_id` is not null", "type": "query", "params": [], "bindings": [54], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/OrderCreatedNotification.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\OrderCreatedNotification.php", "line": 19}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 508}, {"index": 29, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 321}], "start": **********.937761, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 32.846, "width_percent": 0.366}, {"sql": "select * from `ec_products` where `ec_products`.`id` in (43) and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published'))", "type": "query", "params": [], "bindings": [1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 27, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 28, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/OrderCreatedNotification.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\OrderCreatedNotification.php", "line": 19}, {"index": 32, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 508}], "start": **********.940143, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 33.211, "width_percent": 0.366}, {"sql": "insert into `admin_notifications` (`title`, `action_label`, `action_url`, `description`, `permission`, `updated_at`, `created_at`) values ('New order #10000054', 'View', '/admin/ecommerce/orders/edit/54', '<PERSON><PERSON> ordered 1 product(s)', '', '2025-08-15 17:17:30', '2025-08-15 17:17:30')", "type": "query", "params": [], "bindings": ["New order #10000054", "View", "/admin/ecommerce/orders/edit/54", "Fae Koelpin ordered 1 product(s)", "", "2025-08-15 17:17:30", "2025-08-15 17:17:30"], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "vendor/botble/platform/base/src/Listeners/AdminNotificationListener.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Listeners\\AdminNotificationListener.php", "line": 22}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 508}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 508}, {"index": 30, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 321}, {"index": 34, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 46}], "start": **********.947191, "duration": 0.00426, "duration_str": "4.26ms", "memory": 0, "memory_str": null, "filename": "AdminNotificationListener.php:22", "source": {"index": 18, "namespace": null, "name": "vendor/botble/platform/base/src/Listeners/AdminNotificationListener.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Listeners\\AdminNotificationListener.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FListeners%2FAdminNotificationListener.php&line=22", "ajax": false, "filename": "AdminNotificationListener.php", "line": "22"}, "connection": "martfury", "explain": null, "start_percent": 33.577, "width_percent": 3.386}, {"sql": "select * from `ec_customers` where `ec_customers`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/AbandonedCartService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\AbandonedCartService.php", "line": 58}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/MarkCartAsRecovered.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\MarkCartAsRecovered.php", "line": 17}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 508}], "start": **********.954683, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 36.963, "width_percent": 0.421}, {"sql": "select * from `ec_abandoned_carts` where `is_recovered` = 0 and (`customer_id` = 2) order by `created_at` desc limit 1", "type": "query", "params": [], "bindings": [0, 2], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/AbandonedCartService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\AbandonedCartService.php", "line": 71}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/MarkCartAsRecovered.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\MarkCartAsRecovered.php", "line": 17}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 508}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 321}], "start": **********.957371, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 37.385, "width_percent": 0.254}, {"sql": "update `ec_orders` set `is_finished` = 1, `ec_orders`.`updated_at` = '2025-08-15 17:17:30' where `id` = 54", "type": "query", "params": [], "bindings": [1, "2025-08-15 17:17:30", 54], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 114}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 321}, {"index": 20, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 46}, {"index": 22, "namespace": null, "name": "platform/plugins/hyperpay/src/Http/Controllers/HyperPayController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\hyperpay\\src\\Http\\Controllers\\HyperPayController.php", "line": 84}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.9599, "duration": 0.00439, "duration_str": "4.39ms", "memory": 0, "memory_str": null, "filename": "OrderHelper.php:114", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 114}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FSupports%2FOrderHelper.php&line=114", "ajax": false, "filename": "OrderHelper.php", "line": "114"}, "connection": "martfury", "explain": null, "start_percent": 37.639, "width_percent": 3.49}, {"sql": "select * from `ec_products` where `ec_products`.`id` = 43 and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published')) limit 1", "type": "query", "params": [], "bindings": [43, 1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 209}, {"index": 19, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 116}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 321}, {"index": 25, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 46}], "start": **********.967536, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 41.129, "width_percent": 0.437}, {"sql": "update `ec_products` set `quantity` = 13, `ec_products`.`updated_at` = '2025-08-15 17:17:30' where `id` = 43", "type": "query", "params": [], "bindings": [13, "2025-08-15 17:17:30", 43], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 218}, {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 116}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 321}, {"index": 21, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 46}, {"index": 23, "namespace": null, "name": "platform/plugins/hyperpay/src/Http/Controllers/HyperPayController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\hyperpay\\src\\Http\\Controllers\\HyperPayController.php", "line": 84}], "start": **********.9721198, "duration": 0.0048200000000000005, "duration_str": "4.82ms", "memory": 0, "memory_str": null, "filename": "OrderHelper.php:218", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 218}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FSupports%2FOrderHelper.php&line=218", "ajax": false, "filename": "OrderHelper.php", "line": "218"}, "connection": "martfury", "explain": null, "start_percent": 41.566, "width_percent": 3.831}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`product_id` = 43 and `ec_product_variations`.`product_id` is not null limit 1", "type": "query", "params": [], "bindings": [43], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 430}, {"index": 30, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 149}], "start": **********.979905, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 45.397, "width_percent": 0.278}, {"sql": "select * from `ec_products` where `ec_products`.`id` = 10 and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published')) limit 1", "type": "query", "params": [], "bindings": [10, 1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 430}, {"index": 30, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 149}], "start": **********.9821608, "duration": 0.0040999999999999995, "duration_str": "4.1ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 45.676, "width_percent": 3.259}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 10 and `ec_product_variations`.`configurable_product_id` is not null and `ec_product_variations`.`is_default` = 1 limit 1", "type": "query", "params": [], "bindings": [10, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 149}, {"index": 30, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 218}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 116}], "start": **********.98766, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 48.935, "width_percent": 0.715}, {"sql": "update `ec_products` set `quantity` = 13, `stock_status` = 'in_stock', `ec_products`.`updated_at` = '2025-08-15 17:17:30' where `id` = 10", "type": "query", "params": [], "bindings": [13, {"value": "in_stock", "label": "In stock"}, "2025-08-15 17:17:30", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/UpdateDefaultProductService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\UpdateDefaultProductService.php", "line": 37}, {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 150}, {"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 218}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 116}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 321}], "start": **********.000721, "duration": 0.00428, "duration_str": "4.28ms", "memory": 0, "memory_str": null, "filename": "UpdateDefaultProductService.php:37", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/UpdateDefaultProductService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\UpdateDefaultProductService.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FServices%2FProducts%2FUpdateDefaultProductService.php&line=37", "ajax": false, "filename": "UpdateDefaultProductService.php", "line": "37"}, "connection": "martfury", "explain": null, "start_percent": 49.65, "width_percent": 3.402}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 10 and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/UpdateProductStockStatus.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\UpdateProductStockStatus.php", "line": 21}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 508}, {"index": 20, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 156}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/UpdateDefaultProductService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\UpdateDefaultProductService.php", "line": 37}, {"index": 28, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 150}], "start": **********.009149, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "UpdateProductStockStatus.php:21", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/UpdateProductStockStatus.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\UpdateProductStockStatus.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FListeners%2FUpdateProductStockStatus.php&line=21", "ajax": false, "filename": "UpdateProductStockStatus.php", "line": "21"}, "connection": "martfury", "explain": null, "start_percent": 53.052, "width_percent": 0.278}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 10 and `ec_product_variations`.`configurable_product_id` is not null", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/UpdateProductStockStatus.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\UpdateProductStockStatus.php", "line": 39}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/UpdateProductStockStatus.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\UpdateProductStockStatus.php", "line": 22}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 508}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 156}], "start": **********.010155, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 53.331, "width_percent": 0.31}, {"sql": "select * from `ec_products` where `ec_products`.`id` in (43) and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published'))", "type": "query", "params": [], "bindings": [1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/UpdateProductStockStatus.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\UpdateProductStockStatus.php", "line": 39}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/UpdateProductStockStatus.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\UpdateProductStockStatus.php", "line": 22}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 508}], "start": **********.0120451, "duration": 0.00396, "duration_str": "3.96ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 53.641, "width_percent": 3.148}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 10 and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 159}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/UpdateDefaultProductService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\UpdateDefaultProductService.php", "line": 37}, {"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 150}, {"index": 29, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 218}, {"index": 30, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 116}], "start": **********.019711, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Product.php:159", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 159}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=159", "ajax": false, "filename": "Product.php", "line": "159"}, "connection": "martfury", "explain": null, "start_percent": 56.789, "width_percent": 0.39}, {"sql": "select `product_id` from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 10 and `ec_product_variations`.`configurable_product_id` is not null", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 161}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/UpdateDefaultProductService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\UpdateDefaultProductService.php", "line": 37}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 150}, {"index": 32, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 218}, {"index": 33, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 116}], "start": **********.0212138, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "Product.php:161", "source": {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 161}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=161", "ajax": false, "filename": "Product.php", "line": "161"}, "connection": "martfury", "explain": null, "start_percent": 57.178, "width_percent": 0.628}, {"sql": "update `ec_products` set `name` = '<PERSON><PERSON><PERSON> Leather Duffle Bag In Brown Color', `minimum_order_quantity` = 0, `maximum_order_quantity` = 0, `ec_products`.`updated_at` = '2025-08-15 17:17:31' where `id` in (43) and `is_variation` = 1 and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published'))", "type": "query", "params": [], "bindings": ["<PERSON><PERSON><PERSON>g In Brown Color", 0, 0, "2025-08-15 17:17:31", 43, 1, 1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 163}, {"index": 19, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/UpdateDefaultProductService.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Services\\Products\\UpdateDefaultProductService.php", "line": 37}, {"index": 20, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 150}, {"index": 27, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 218}, {"index": 28, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 116}], "start": **********.0247068, "duration": 0.02094, "duration_str": "20.94ms", "memory": 0, "memory_str": null, "filename": "Product.php:163", "source": {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 163}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=163", "ajax": false, "filename": "Product.php", "line": "163"}, "connection": "martfury", "explain": null, "start_percent": 57.806, "width_percent": 16.645}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 10 and `ec_product_variations`.`configurable_product_id` is not null", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/UpdateProductStockStatus.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\UpdateProductStockStatus.php", "line": 39}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/UpdateProductStockStatus.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\UpdateProductStockStatus.php", "line": 34}, {"index": 19, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/UpdateProductStockStatus.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\UpdateProductStockStatus.php", "line": 17}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 508}], "start": **********.049663, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 74.452, "width_percent": 0.278}, {"sql": "select * from `ec_products` where `ec_products`.`id` in (43) and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published'))", "type": "query", "params": [], "bindings": [1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/UpdateProductStockStatus.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\UpdateProductStockStatus.php", "line": 39}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/UpdateProductStockStatus.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\UpdateProductStockStatus.php", "line": 34}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/UpdateProductStockStatus.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\UpdateProductStockStatus.php", "line": 17}], "start": **********.05153, "duration": 0.00395, "duration_str": "3.95ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 74.73, "width_percent": 3.14}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 10 and `ec_product_variations`.`configurable_product_id` is not null", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/UpdateProductStockStatus.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\UpdateProductStockStatus.php", "line": 39}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/UpdateProductStockStatus.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\UpdateProductStockStatus.php", "line": 34}, {"index": 19, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/UpdateProductStockStatus.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\UpdateProductStockStatus.php", "line": 17}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 508}], "start": **********.060454, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 77.87, "width_percent": 0.318}, {"sql": "select * from `ec_products` where `ec_products`.`id` in (43) and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published'))", "type": "query", "params": [], "bindings": [1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/UpdateProductStockStatus.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\UpdateProductStockStatus.php", "line": 39}, {"index": 24, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/UpdateProductStockStatus.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\UpdateProductStockStatus.php", "line": 34}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/UpdateProductStockStatus.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Listeners\\UpdateProductStockStatus.php", "line": 17}], "start": **********.062822, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 78.188, "width_percent": 0.366}, {"sql": "select * from `mp_stores` where `mp_stores`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 709}, {"index": 24, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 737}, {"index": 25, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 693}], "start": **********.0683348, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 78.553, "width_percent": 0.318}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_type` = 'Botble\\\\Marketplace\\\\Models\\\\Store' and `slugs`.`reference_id` = 2 and `slugs`.`reference_id` is not null limit 1", "type": "query", "params": [], "bindings": ["Botble\\Marketplace\\Models\\Store", 2], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "vendor/botble/slug/src/Providers/SlugServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\slug\\src\\Providers\\SlugServiceProvider.php", "line": 118}, {"index": 29, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 25}, {"index": 30, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 712}], "start": **********.0726469, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 78.871, "width_percent": 0.318}, {"sql": "select * from `ec_shipping_rules` where `ec_shipping_rules`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 474}, {"index": 20, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Order.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Order.php", "line": 94}, {"index": 27, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 28, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 722}], "start": **********.0956008, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 79.189, "width_percent": 0.739}, {"sql": "select * from `payments` where `payments`.`id` = 95 limit 1", "type": "query", "params": [], "bindings": [95], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 723}, {"index": 24, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 737}, {"index": 25, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 693}], "start": **********.098123, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 79.928, "width_percent": 0.278}, {"sql": "insert into `ec_order_histories` (`action`, `description`, `order_id`, `updated_at`, `created_at`) values ('send_order_confirmation_email', 'The email confirmation was sent to customer', 54, '2025-08-15 17:17:31', '2025-08-15 17:17:31')", "type": "query", "params": [], "bindings": [{"value": "send_order_confirmation_email", "label": "send_order_confirmation_email"}, "The email confirmation was sent to customer", 54, "2025-08-15 17:17:31", "2025-08-15 17:17:31"], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 742}, {"index": 19, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 693}, {"index": 23, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 26, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 321}, {"index": 30, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 46}], "start": **********.1017482, "duration": 0.00378, "duration_str": "3.78ms", "memory": 0, "memory_str": null, "filename": "OrderSupportServiceProvider.php:742", "source": {"index": 18, "namespace": null, "name": "platform/plugins/marketplace/src/Providers/OrderSupportServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php", "line": 742}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FProviders%2FOrderSupportServiceProvider.php&line=742", "ajax": false, "filename": "OrderSupportServiceProvider.php", "line": "742"}, "connection": "martfury", "explain": null, "start_percent": 80.207, "width_percent": 3.005}, {"sql": "select * from `ec_shipping_rules` where `ec_shipping_rules`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 474}, {"index": 20, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Order.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Order.php", "line": 94}, {"index": 27, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 28, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 281}], "start": **********.113239, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 83.211, "width_percent": 0.397}, {"sql": "select * from `ec_shipments` where `ec_shipments`.`order_id` = 54 and `ec_shipments`.`order_id` is not null limit 1", "type": "query", "params": [], "bindings": [54], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": "view", "name": "plugins/ecommerce::emails.partials.order-delivery-notes", "file": "D:\\laragon\\www\\martfury\\platform/plugins/ecommerce/resources/views/emails/partials/order-delivery-notes.blade.php", "line": 1}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 57}], "start": **********.1165369, "duration": 0.00429, "duration_str": "4.29ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 83.609, "width_percent": 3.41}, {"sql": "select * from `ec_flash_sales` where date(`end_date`) >= '2025-08-15' and `status` = 'published' order by `created_at` desc", "type": "query", "params": [], "bindings": ["2025-08-15", "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/FlashSaleRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\FlashSaleRepository.php", "line": 35}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/FlashSaleSupport.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\FlashSaleSupport.php", "line": 52}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/FlashSaleSupport.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\FlashSaleSupport.php", "line": 22}, {"index": 19, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Concerns/ProductPrices.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Concerns\\ProductPrices.php", "line": 46}], "start": **********.134775, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 87.019, "width_percent": 0.318}, {"sql": "select * from `ec_customers` where `id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/DiscountSupport.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\DiscountSupport.php", "line": 22}], "start": **********.138532, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 87.337, "width_percent": 0.318}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`product_id` = 43 and `ec_product_variations`.`product_id` is not null limit 1", "type": "query", "params": [], "bindings": [43], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 430}, {"index": 30, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Concerns/ProductPrices.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Concerns\\ProductPrices.php", "line": 58}], "start": **********.141163, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 87.655, "width_percent": 0.246}, {"sql": "select * from `ec_products` where `ec_products`.`id` = 10 and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published')) limit 1", "type": "query", "params": [], "bindings": [10, 1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 430}, {"index": 30, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 31, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Concerns/ProductPrices.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Concerns\\ProductPrices.php", "line": 58}], "start": **********.1431139, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 87.901, "width_percent": 1.002}, {"sql": "select * from `ec_discounts` where `type` = 'promotion' and `start_date` <= '2025-08-15 17:17:31' and (`end_date` is null or `end_date` >= '2025-08-15 17:17:31') and (`target` in ('all-orders', 'amount-minimum-order') or (`target` in ('customer', 'group-products', 'products-by-category', 'specific-product', 'product-variant') and `product_quantity` = 1))", "type": "query", "params": [], "bindings": ["promotion", "2025-08-15 17:17:31", "2025-08-15 17:17:31", "all-orders", "amount-minimum-order", "customer", "group-products", "products-by-category", "specific-product", "product-variant", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/DiscountRepository.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\DiscountRepository.php", "line": 50}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/DiscountSupport.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\DiscountSupport.php", "line": 116}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/DiscountSupport.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\DiscountSupport.php", "line": 42}, {"index": 19, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Concerns/ProductPrices.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Models\\Concerns\\ProductPrices.php", "line": 58}], "start": **********.147481, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 88.903, "width_percent": 0.596}, {"sql": "select * from `ec_order_addresses` where `ec_order_addresses`.`order_id` = 54 and `ec_order_addresses`.`order_id` is not null and `type` = 'shipping_address' limit 1", "type": "query", "params": [], "bindings": [54, "shipping_address"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 301}, {"index": 25, "namespace": null, "name": "platform/plugins/marketplace/src/Supports/MarketplaceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Supports\\MarketplaceHelper.php", "line": 114}, {"index": 26, "namespace": null, "name": "platform/plugins/marketplace/src/Supports/MarketplaceHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Supports\\MarketplaceHelper.php", "line": 103}], "start": **********.1712182, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 89.499, "width_percent": 0.358}, {"sql": "insert into `email_logs` (`from`, `to`, `cc`, `bcc`, `subject`, `html_body`, `text_body`, `raw_body`, `debug_info`, `updated_at`, `created_at`) values ('\\\"Example\\\" <<EMAIL>>', '<EMAIL>', '', '', 'New order(s) at MartFury', '<!doctype html>\\n<html lang=\\\"en\\\">\\n\\n<head>\\n    <meta charset=\\\"UTF-8\\\">\\n    <title>MartFury</title>\\n</head>\\n\\n<body class=\\\"bb-bg-body\\\" dir=\\\"ltr\\\" style=\\\"margin: 0; padding: 0; font-size: 14px; line-height: 171.4285714286%; mso-line-height-rule: exactly; color: #3A4859; width: 100%; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale; -webkit-font-feature-settings: \\'cv02\\', \\'cv03\\', \\'cv04\\', \\'cv11\\'; font-feature-settings: \\'cv02\\', \\'cv03\\', \\'cv04\\', \\'cv11\\'; font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; background-color: #f6f7f9;\\\">\\n\\n<center>\\n    <table class=\\\"bb-main bb-bg-body\\\" width=\\\"100%\\\" cellspacing=\\\"0\\\" cellpadding=\\\"0\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; background-color: #f6f7f9;\\\">\\n        <tbody>\\n            <tr>\\n                <td align=\\\"center\\\" valign=\\\"top\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;\\\">\\n                    <table class=\\\"bb-wrap\\\" cellspacing=\\\"0\\\" cellpadding=\\\"0\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; max-width: 640px; text-align: left; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\\\">\\n                        <tbody>\\n                            <tr>\\n                                <td class=\\\"bb-p-sm\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 8px;\\\">\\n                                    <table cellpadding=\\\"0\\\" cellspacing=\\\"0\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\\\">\\n                                        <tbody>\\n                                            <tr>\\n                                                <td class=\\\"bb-py-lg\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding-top: 24px; padding-bottom: 24px;\\\">\\n                                                    <table cellspacing=\\\"0\\\" cellpadding=\\\"0\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\\\">\\n                                                        <tbody>\\n                                                            <tr>\\n                                                                <td class=\\\"bb-text-left\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: left;\\\">\\n                                                                    <a href=\\\"https://martfury.gc\\\" style=\\\"color: #206bc4; text-decoration: none;\\\">\\n                                                                        <img class=\\\"bb-logo\\\" src=\\\"https://martfury.gc/storage/general/logo.png\\\" alt=\\\"MartFury\\\" style=\\\"line-height: 100%; outline: none; text-decoration: none; vertical-align: baseline; font-size: 0; border: 0 none; max-height: 40px;\\\">\\n                                                                    </a>\\n                                                                </td>\\n                                                                <td class=\\\"bb-text-right\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right;\\\">\\n                                                                    2025-08-15 17:17:31\\n                                                                </td>\\n                                                            </tr>\\n                                                        </tbody>\\n                                                    </table>\\n                                                </td>\\n                                            </tr>\\n                                        </tbody>\\n                                    </table>\\n\\n\\n<div class=\\\"bb-main-content\\\">\\n    <table class=\\\"bb-box\\\" cellpadding=\\\"0\\\" cellspacing=\\\"0\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; background: #ffffff; border-radius: 4px; -webkit-box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05); box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05); border: 1px solid #dce0e5; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\\\">\\n        <tbody>\\n            <tr>\\n                <td class=\\\"bb-content bb-pb-0\\\" align=\\\"center\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 40px 48px; padding-bottom: 0;\\\">\\n                    <table class=\\\"bb-icon bb-icon-lg bb-bg-blue\\\" cellspacing=\\\"0\\\" cellpadding=\\\"0\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 0; border-radius: 50%; background: #edeef0; line-height: 100%; font-weight: 300; border-collapse: separate; text-align: center; width: 72px; height: 72px; font-size: 48px; background-color: #206bc4; color: #ffffff; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\\\">\\n                        <tbody>\\n                            <tr>\\n                                <td valign=\\\"middle\\\" align=\\\"center\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;\\\">\\n                                    <img src=\\\"https://martfury.gc/vendor/core/core/base/images/email-icons/shopping-cart.png\\\" class=\\\"bb-va-middle\\\" width=\\\"40\\\" height=\\\"40\\\" alt=\\\"Icon\\\" style=\\\"border: 0 none; line-height: 100%; outline: none; text-decoration: none; font-size: 0; vertical-align: middle; display: block; width: 40px; height: 40px;\\\">\\n                                </td>\\n                            </tr>\\n                        </tbody>\\n                    </table>\\n                    <h1 class=\\\"bb-text-center bb-m-0 bb-mt-md\\\" style=\\\"font-weight: 600; color: #232b42; font-size: 28px; line-height: 130%; text-align: center; margin: 0; margin-top: 16px;\\\">Order successfully!</h1>\\n                </td>\\n            </tr>\\n            <tr>\\n                <td class=\\\"bb-content\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 40px 48px;\\\">\\n                    <div>Dear Global Office,</div>\\n                    <div>You got a new order on MartFury!</div>\\n                </td>\\n            </tr>\\n            <tr>\\n                <td class=\\\"bb-content bb-pt-0\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 40px 48px; padding-top: 0;\\\">\\n                    <table class=\\\"bb-row bb-mb-md\\\" cellpadding=\\\"0\\\" cellspacing=\\\"0\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; table-layout: fixed; margin-bottom: 16px; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\\\">\\n                        <tbody>\\n                            <tr>\\n                                <td class=\\\"bb-bb-col\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;\\\">\\n                                    <h4 class=\\\"bb-m-0\\\" style=\\\"font-weight: 600; color: #232b42; font-size: 16px; margin: 0;\\\">Customer Information</h4>\\n                                    <div>Name: <strong style=\\\"font-weight: 600;\\\">Fae Koelpin</strong>\\n</div>\\n                                                                        <div>Phone: <strong style=\\\"font-weight: 600;\\\">+18598017656</strong>\\n</div>\\n                                                                                                                                                <div>Address: <strong style=\\\"font-weight: 600;\\\">2358 Jadon Stream, Bashirianfurt, Utah, PE</strong>\\n</div>\\n                                                                    </td>\\n                            </tr>\\n                        </tbody>\\n                    </table>\\n                </td>\\n            </tr>\\n            <tr>\\n                <td class=\\\"bb-content bb-pt-0\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 40px 48px; padding-top: 0;\\\">\\n                    <h4 style=\\\"font-weight: 600; margin: 0 0 0.5em; color: #232b42; font-size: 16px;\\\">Here\\'s what you ordered:</h4>\\n                    <a class=\\\"button button-blue\\\" href=\\\"https://martfury.gc/orders/tracking?order_id=%2310000054&amp;email=vendor%40botble.com\\\" style=\\\"color: #206bc4; text-decoration: none;\\\">View order</a>\\n    or <a href=\\\"https://martfury.gc\\\" style=\\\"color: #206bc4; text-decoration: none;\\\">Go to our shop</a>\\n\\n    <br>\\n\\n<table class=\\\"bb-table\\\" cellspacing=\\\"0\\\" cellpadding=\\\"0\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\\\">\\n    <thead>\\n        <tr>\\n            <th colspan=\\\"2\\\" style=\\\"text-transform: uppercase; font-weight: 600; color: #667382; font-size: 12px; padding: 0 0 4px 0; padding-left: 0;\\\"></th>\\n            <th style=\\\"text-transform: uppercase; font-weight: 600; color: #667382; font-size: 12px; padding: 0 0 4px 0;\\\">Quantity</th>\\n            <th class=\\\"bb-text-right\\\" style=\\\"text-align: right; text-transform: uppercase; font-weight: 600; color: #667382; font-size: 12px; padding: 0 0 4px 0; padding-right: 0;\\\">Price</th>\\n        </tr>\\n    </thead>\\n\\n    <tbody>\\n                <tr>\\n            <td class=\\\"bb-pr-0\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding-right: 0; padding: 4px 0; padding-left: 0;\\\">\\n                <a href=\\\"\\\" style=\\\"color: #206bc4; text-decoration: none;\\\">\\n                    <img src=\\\"https://martfury.gc/storage/products/10-150x150.jpg\\\" class=\\\" bb-rounded\\\" width=\\\"64\\\" height=\\\"64\\\" alt=\\\"\\\" style=\\\"line-height: 100%; outline: none; text-decoration: none; vertical-align: baseline; font-size: 0; border: 0 none; border-radius: 4px;\\\">\\n                </a>\\n            </td>\\n            <td class=\\\"bb-pl-md bb-w-100p\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; width: 100%; padding-left: 16px !important; padding: 4px 0;\\\">\\n                <strong style=\\\"font-weight: 600;\\\">Herschel Leather Duffle Bag In Brown Color</strong><br>\\n                                    <span class=\\\"bb-text-muted\\\" style=\\\"color: #667382;\\\">(Color: Blue, Size: L)</span>\\n                \\n                            </td>\\n            <td class=\\\"bb-text-center\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: center; padding: 4px 0;\\\">x 1</td>\\n            <td class=\\\"bb-text-right\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right; padding: 4px 0; padding-right: 0;\\\">SAR1,294.00</td>\\n        </tr>\\n    \\n                                    <tr>\\n                    <td colspan=\\\"2\\\" class=\\\"bb-border-top bb-text-right\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right; border-top: 1px solid #dce0e5; padding: 4px 0; padding-left: 0;\\\">Subtotal</td>\\n                    <td colspan=\\\"2\\\" class=\\\"bb-border-top bb-text-right\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right; border-top: 1px solid #dce0e5; padding: 4px 0; padding-right: 0;\\\">SAR1,294.00</td>\\n                </tr>\\n            \\n            \\n                            <tr>\\n                    <td colspan=\\\"2\\\" class=\\\"bb-text-right\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right; padding: 4px 0; padding-left: 0;\\\">Tax</td>\\n                    <td colspan=\\\"2\\\" class=\\\"bb-text-right\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right; padding: 4px 0; padding-right: 0;\\\">SAR129.40</td>\\n                </tr>\\n            \\n            \\n                        <tr>\\n                <td colspan=\\\"2\\\" class=\\\"bb-text-right bb-font-strong bb-h3 bb-m-0\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; color: #232b42; font-size: 20px; line-height: 130%; text-align: right; font-weight: 600; margin: 0; padding: 4px 0; padding-left: 0;\\\">Total</td>\\n                <td colspan=\\\"2\\\" class=\\\"bb-font-strong bb-h3 bb-m-0 bb-text-right\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; color: #232b42; font-size: 20px; line-height: 130%; text-align: right; font-weight: 600; margin: 0; padding: 4px 0; padding-right: 0;\\\">SAR1,423.40</td>\\n            </tr>\\n            </tbody>\\n</table>\\n\\n\\n                                    </td>\\n            </tr>\\n            <tr>\\n                <td class=\\\"bb-content bb-border-top\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 40px 48px; border-top: 1px solid #dce0e5;\\\">\\n                    <table class=\\\"bb-row bb-mb-md\\\" cellpadding=\\\"0\\\" cellspacing=\\\"0\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; table-layout: fixed; margin-bottom: 16px; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\\\">\\n                        <tbody>\\n                            <tr>\\n                                <td class=\\\"bb-bb-col\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;\\\">\\n                                    <h4 class=\\\"bb-m-0\\\" style=\\\"font-weight: 600; color: #232b42; font-size: 16px; margin: 0;\\\">Order number</h4>\\n                                    <div>#10000054</div>\\n                                </td>\\n                                <td class=\\\"bb-col-spacer\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; vertical-align: top; width: 24px;\\\"></td>\\n                                <td class=\\\"bb-col\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; vertical-align: top;\\\">\\n                                    <h4 class=\\\"bb-mb-0\\\" style=\\\"font-weight: 600; margin: 0 0 0.5em; color: #232b42; font-size: 16px; margin-bottom: 0;\\\">Order date</h4>\\n                                    <div>2025-08-15 17:14:38</div>\\n                                </td>\\n                            </tr>\\n                        </tbody>\\n                    </table>\\n                    <table class=\\\"bb-row\\\" cellpadding=\\\"0\\\" cellspacing=\\\"0\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; table-layout: fixed; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\\\">\\n                        <tbody>\\n                            <tr>\\n                                <td class=\\\"bb-col\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; vertical-align: top;\\\">\\n                                                                        <h4 class=\\\"bb-m-0\\\" style=\\\"font-weight: 600; color: #232b42; font-size: 16px; margin: 0;\\\">Shipping Method</h4>\\n                                    <div>\\n                                        Free delivery\\n                                    </div>\\n                                                                    </td>\\n\\n                                <td class=\\\"bb-col-spacer\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; vertical-align: top; width: 24px;\\\"></td>\\n                                <td class=\\\"bb-col\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; vertical-align: top;\\\">\\n                                    <h4 class=\\\"bb-m-0\\\" style=\\\"font-weight: 600; color: #232b42; font-size: 16px; margin: 0;\\\">Payment Method</h4>\\n                                    <div>\\n                                        HyperPay\\n                                    </div>\\n                                </td>\\n                            </tr>\\n                        </tbody>\\n                    </table>\\n                </td>\\n            </tr>\\n        </tbody>\\n    </table>\\n</div>\\n\\n<table cellspacing=\\\"0\\\" cellpadding=\\\"0\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\\\">\\n    <tbody>\\n        <tr>\\n            <td class=\\\"bb-py-xl\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding-top: 48px; padding-bottom: 48px;\\\">\\n                <table class=\\\"bb-text-center bb-text-muted\\\" cellspacing=\\\"0\\\" cellpadding=\\\"0\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; color: #667382; text-align: center; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\\\">\\n                    <tbody>\\n                    \\n                    <tr>\\n                        <td class=\\\"bb-px-lg\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding-right: 24px; padding-left: 24px;\\\">\\n                            © 2025 MartFury. All Rights Reserved.\\n                        </td>\\n                    </tr>\\n\\n                                            <tr>\\n                            <td class=\\\"bb-pt-md\\\" style=\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding-top: 16px;\\\">\\n                                If you have any questions, feel free to message us at <a href=\\\"mailto:<EMAIL>\\\" style=\\\"color: #206bc4; text-decoration: none;\\\"><EMAIL></a>.\\n                            </td>\\n                        </tr>\\n                                        </tbody>\\n                </table>\\n            </td>\\n        </tr>\\n    </tbody>\\n</table>\\n</td>\\n</tr>\\n</tbody>\\n</table>\\n</td>\\n</tr>\\n</tbody>\\n</table>\\n</center>\\n</body>\\n\\n</html>', null, 'From: Example <<EMAIL>>\\r\\nTo: <EMAIL>\\r\\nSubject: New order(s) at MartFury\\r\\nMessage-ID: <<EMAIL>>\\r\\nMIME-Version: 1.0\\r\\nDate: Fri, 15 Aug 2025 17:17:31 +0000\\r\\nContent-Type: text/html; charset=utf-8\\r\\nContent-Transfer-Encoding: quoted-printable\\r\\n\\r\\n<!doctype html>\\r\\n<html lang=3D\\\"en\\\">\\r\\n\\r\\n<head>\\r\\n    <meta charset=3D\\\"UTF=\\r\\n-8\\\">\\r\\n    <title>MartFury</title>\\r\\n</head>\\r\\n\\r\\n<body class=3D\\\"bb-bg-body=\\r\\n\\\" dir=3D\\\"ltr\\\" style=3D\\\"margin: 0; padding: 0; font-size: 14px; line-height:=\\r\\n 171.4285714286%; mso-line-height-rule: exactly; color: #3A4859; width: 100=\\r\\n%; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale;=\\r\\n -webkit-font-feature-settings: \\'cv02\\', \\'cv03\\', \\'cv04\\', \\'cv11\\'; font-featur=\\r\\ne-settings: \\'cv02\\', \\'cv03\\', \\'cv04\\', \\'cv11\\'; font-family: Inter, -apple-syst=\\r\\nem, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sa=\\r\\nns-serif; background-color: #f6f7f9;\\\">\\r\\n\\r\\n<center>\\r\\n    <table class=3D\\\"=\\r\\nbb-main bb-bg-body\\\" width=3D\\\"100%\\\" cellspacing=3D\\\"0\\\" cellpadding=3D\\\"0\\\" styl=\\r\\ne=3D\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, =\\r\\nSegoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; wi=\\r\\ndth: 100%; -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; back=\\r\\nground-color: #f6f7f9;\\\">\\r\\n        <tbody>\\r\\n            <tr>\\r\\n           =\\r\\n     <td align=3D\\\"center\\\" valign=3D\\\"top\\\" style=3D\\\"font-family: Inter, -appl=\\r\\ne-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Ne=\\r\\nue, sans-serif;\\\">\\r\\n                    <table class=3D\\\"bb-wrap\\\" cellspacin=\\r\\ng=3D\\\"0\\\" cellpadding=3D\\\"0\\\" style=3D\\\"font-family: Inter, -apple-system, Blink=\\r\\nMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;=\\r\\n border-collapse: collapse; width: 100%; max-width: 640px; text-align: left=\\r\\n; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\\\">\\r\\n               =\\r\\n         <tbody>\\r\\n                            <tr>\\r\\n                     =\\r\\n           <td class=3D\\\"bb-p-sm\\\" style=3D\\\"font-family: Inter, -apple-system=\\r\\n, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans=\\r\\n-serif; padding: 8px;\\\">\\r\\n                                    <table cellpa=\\r\\ndding=3D\\\"0\\\" cellspacing=3D\\\"0\\\" style=3D\\\"font-family: Inter, -apple-system, B=\\r\\nlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-se=\\r\\nrif; border-collapse: collapse; width: 100%; -premailer-cellpadding: 0; -pr=\\r\\nemailer-cellspacing: 0;\\\">\\r\\n                                        <tbody>=\\r\\n\\r\\n                                            <tr>\\r\\n                     =\\r\\n                           <td class=3D\\\"bb-py-lg\\\" style=3D\\\"font-family: Int=\\r\\ner, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Hel=\\r\\nvetica Neue, sans-serif; padding-top: 24px; padding-bottom: 24px;\\\">\\r\\n     =\\r\\n                                               <table cellspacing=3D\\\"0\\\" cel=\\r\\nlpadding=3D\\\"0\\\" style=3D\\\"font-family: Inter, -apple-system, BlinkMacSystemFo=\\r\\nnt, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-col=\\r\\nlapse: collapse; width: 100%; -premailer-cellpadding: 0; -premailer-cellspa=\\r\\ncing: 0;\\\">\\r\\n                                                        <tbody=\\r\\n>\\r\\n                                                            <tr>\\r\\n    =\\r\\n                                                            <td class=3D\\\"bb=\\r\\n-text-left\\\" style=3D\\\"font-family: Inter, -apple-system, BlinkMacSystemFont,=\\r\\n San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: l=\\r\\neft;\\\">\\r\\n                                                                  =\\r\\n  <a href=3D\\\"https://martfury.gc\\\" style=3D\\\"color: #206bc4; text-decoration:=\\r\\n none;\\\">\\r\\n                                                                =\\r\\n        <img class=3D\\\"bb-logo\\\" src=3D\\\"https://martfury.gc/storage/general/l=\\r\\nogo.png\\\" alt=3D\\\"MartFury\\\" style=3D\\\"line-height: 100%; outline: none; text-d=\\r\\necoration: none; vertical-align: baseline; font-size: 0; border: 0 none; ma=\\r\\nx-height: 40px;\\\">\\r\\n                                                       =\\r\\n             </a>\\r\\n                                                       =\\r\\n         </td>\\r\\n                                                          =\\r\\n      <td class=3D\\\"bb-text-right\\\" style=3D\\\"font-family: Inter, -apple-syste=\\r\\nm, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, san=\\r\\ns-serif; text-align: right;\\\">\\r\\n                                           =\\r\\n                         2025-08-15 17:17:31\\r\\n                            =\\r\\n                                    </td>\\r\\n                               =\\r\\n                             </tr>\\r\\n                                      =\\r\\n                  </tbody>\\r\\n                                              =\\r\\n      </table>\\r\\n                                                </td>\\r\\n  =\\r\\n                                          </tr>\\r\\n                         =\\r\\n               </tbody>\\r\\n                                    </table>\\r\\n=\\r\\n\\r\\n\\r\\n<div class=3D\\\"bb-main-content\\\">\\r\\n    <table class=3D\\\"bb-box\\\" cellpad=\\r\\nding=3D\\\"0\\\" cellspacing=3D\\\"0\\\" style=3D\\\"font-family: Inter, -apple-system, Bl=\\r\\ninkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-ser=\\r\\nif; border-collapse: collapse; width: 100%; background: #ffffff; border-rad=\\r\\nius: 4px; -webkit-box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05); box-shadow: 0 =\\r\\n1px 4px rgba(0, 0, 0, 0.05); border: 1px solid #dce0e5; -premailer-cellpadd=\\r\\ning: 0; -premailer-cellspacing: 0;\\\">\\r\\n        <tbody>\\r\\n            <tr>=\\r\\n\\r\\n                <td class=3D\\\"bb-content bb-pb-0\\\" align=3D\\\"center\\\" style=\\r\\n=3D\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, S=\\r\\negoe UI, Roboto, Helvetica Neue, sans-serif; padding: 40px 48px; padding-bo=\\r\\nttom: 0;\\\">\\r\\n                    <table class=3D\\\"bb-icon bb-icon-lg bb-bg-b=\\r\\nlue\\\" cellspacing=3D\\\"0\\\" cellpadding=3D\\\"0\\\" style=3D\\\"font-family: Inter, -appl=\\r\\ne-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Ne=\\r\\nue, sans-serif; padding: 0; border-radius: 50%; background: #edeef0; line-h=\\r\\neight: 100%; font-weight: 300; border-collapse: separate; text-align: cente=\\r\\nr; width: 72px; height: 72px; font-size: 48px; background-color: #206bc4; c=\\r\\nolor: #ffffff; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\\\">\\r\\n  =\\r\\n                      <tbody>\\r\\n                            <tr>\\r\\n        =\\r\\n                        <td valign=3D\\\"middle\\\" align=3D\\\"center\\\" style=3D\\\"fon=\\r\\nt-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI=\\r\\n, Roboto, Helvetica Neue, sans-serif;\\\">\\r\\n                                 =\\r\\n   <img src=3D\\\"https://martfury.gc/vendor/core/core/base/images/email-icons=\\r\\n/shopping-cart.png\\\" class=3D\\\"bb-va-middle\\\" width=3D\\\"40\\\" height=3D\\\"40\\\" alt=\\r\\n=3D\\\"Icon\\\" style=3D\\\"border: 0 none; line-height: 100%; outline: none; text-d=\\r\\necoration: none; font-size: 0; vertical-align: middle; display: block; widt=\\r\\nh: 40px; height: 40px;\\\">\\r\\n                                </td>\\r\\n        =\\r\\n                    </tr>\\r\\n                        </tbody>\\r\\n            =\\r\\n        </table>\\r\\n                    <h1 class=3D\\\"bb-text-center bb-m-0 b=\\r\\nb-mt-md\\\" style=3D\\\"font-weight: 600; color: #232b42; font-size: 28px; line-h=\\r\\neight: 130%; text-align: center; margin: 0; margin-top: 16px;\\\">Order succes=\\r\\nsfully!</h1>\\r\\n                </td>\\r\\n            </tr>\\r\\n            <tr>=\\r\\n\\r\\n                <td class=3D\\\"bb-content\\\" style=3D\\\"font-family: Inter, -a=\\r\\npple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica=\\r\\n Neue, sans-serif; padding: 40px 48px;\\\">\\r\\n                    <div>Dear Gl=\\r\\nobal Office,</div>\\r\\n                    <div>You got a new order on MartFu=\\r\\nry!</div>\\r\\n                </td>\\r\\n            </tr>\\r\\n            <tr>\\r\\n=\\r\\n                <td class=3D\\\"bb-content bb-pt-0\\\" style=3D\\\"font-family: Inte=\\r\\nr, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helv=\\r\\netica Neue, sans-serif; padding: 40px 48px; padding-top: 0;\\\">\\r\\n           =\\r\\n         <table class=3D\\\"bb-row bb-mb-md\\\" cellpadding=3D\\\"0\\\" cellspacing=3D\\\"=\\r\\n0\\\" style=3D\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Fran=\\r\\ncisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: colla=\\r\\npse; width: 100%; table-layout: fixed; margin-bottom: 16px; -premailer-cell=\\r\\npadding: 0; -premailer-cellspacing: 0;\\\">\\r\\n                        <tbody>=\\r\\n\\r\\n                            <tr>\\r\\n                                <td c=\\r\\nlass=3D\\\"bb-bb-col\\\" style=3D\\\"font-family: Inter, -apple-system, BlinkMacSyst=\\r\\nemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;\\\">\\r\\n  =\\r\\n                                  <h4 class=3D\\\"bb-m-0\\\" style=3D\\\"font-weight=\\r\\n: 600; color: #232b42; font-size: 16px; margin: 0;\\\">Customer Information</h=\\r\\n4>\\r\\n                                    <div>Name: <strong style=3D\\\"font-w=\\r\\neight: 600;\\\">Fae Koelpin</strong>\\r\\n</div>\\r\\n                              =\\r\\n                                          <div>Phone: <strong style=3D\\\"font=\\r\\n-weight: 600;\\\">+18598017656</strong>\\r\\n</div>\\r\\n                           =\\r\\n                                                                           =\\r\\n                                          <div>Address: <strong style=3D\\\"fo=\\r\\nnt-weight: 600;\\\">2358 Jadon Stream, Bashirianfurt, Utah, PE</strong>\\r\\n</di=\\r\\nv>\\r\\n                                                                    </=\\r\\ntd>\\r\\n                            </tr>\\r\\n                        </tbody>=\\r\\n\\r\\n                    </table>\\r\\n                </td>\\r\\n            </tr>=\\r\\n\\r\\n            <tr>\\r\\n                <td class=3D\\\"bb-content bb-pt-0\\\" styl=\\r\\ne=3D\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, =\\r\\nSegoe UI, Roboto, Helvetica Neue, sans-serif; padding: 40px 48px; padding-t=\\r\\nop: 0;\\\">\\r\\n                    <h4 style=3D\\\"font-weight: 600; margin: 0 0 0=\\r\\n.5em; color: #232b42; font-size: 16px;\\\">Here\\'s what you ordered:</h4>\\r\\n   =\\r\\n                 <a class=3D\\\"button button-blue\\\" href=3D\\\"https://martfury.g=\\r\\nc/orders/tracking?order_id=3D%2310000054&amp;email=3Dvendor%40botble.com\\\" s=\\r\\ntyle=3D\\\"color: #206bc4; text-decoration: none;\\\">View order</a>\\r\\n    or <a =\\r\\nhref=3D\\\"https://martfury.gc\\\" style=3D\\\"color: #206bc4; text-decoration: none=\\r\\n;\\\">Go to our shop</a>\\r\\n\\r\\n    <br>\\r\\n\\r\\n<table class=3D\\\"bb-table\\\" cellspac=\\r\\ning=3D\\\"0\\\" cellpadding=3D\\\"0\\\" style=3D\\\"font-family: Inter, -apple-system, Bli=\\r\\nnkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-seri=\\r\\nf; border-collapse: collapse; width: 100%; -premailer-cellpadding: 0; -prem=\\r\\nailer-cellspacing: 0;\\\">\\r\\n    <thead>\\r\\n        <tr>\\r\\n            <th cols=\\r\\npan=3D\\\"2\\\" style=3D\\\"text-transform: uppercase; font-weight: 600; color: #667=\\r\\n382; font-size: 12px; padding: 0 0 4px 0; padding-left: 0;\\\"></th>\\r\\n       =\\r\\n     <th style=3D\\\"text-transform: uppercase; font-weight: 600; color: #6673=\\r\\n82; font-size: 12px; padding: 0 0 4px 0;\\\">Quantity</th>\\r\\n            <th c=\\r\\nlass=3D\\\"bb-text-right\\\" style=3D\\\"text-align: right; text-transform: uppercas=\\r\\ne; font-weight: 600; color: #667382; font-size: 12px; padding: 0 0 4px 0; p=\\r\\nadding-right: 0;\\\">Price</th>\\r\\n        </tr>\\r\\n    </thead>\\r\\n\\r\\n    <tbody=\\r\\n>\\r\\n                <tr>\\r\\n            <td class=3D\\\"bb-pr-0\\\" style=3D\\\"font-=\\r\\nfamily: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, =\\r\\nRoboto, Helvetica Neue, sans-serif; padding-right: 0; padding: 4px 0; paddi=\\r\\nng-left: 0;\\\">\\r\\n                <a href=3D\\\"\\\" style=3D\\\"color: #206bc4; text-=\\r\\ndecoration: none;\\\">\\r\\n                    <img src=3D\\\"https://martfury.gc/s=\\r\\ntorage/products/10-150x150.jpg\\\" class=3D\\\" bb-rounded\\\" width=3D\\\"64\\\" height=\\r\\n=3D\\\"64\\\" alt=3D\\\"\\\" style=3D\\\"line-height: 100%; outline: none; text-decoration=\\r\\n: none; vertical-align: baseline; font-size: 0; border: 0 none; border-radi=\\r\\nus: 4px;\\\">\\r\\n                </a>\\r\\n            </td>\\r\\n            <td cla=\\r\\nss=3D\\\"bb-pl-md bb-w-100p\\\" style=3D\\\"font-family: Inter, -apple-system, Blink=\\r\\nMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;=\\r\\n width: 100%; padding-left: 16px !important; padding: 4px 0;\\\">\\r\\n          =\\r\\n      <strong style=3D\\\"font-weight: 600;\\\">Herschel Leather Duffle Bag In Br=\\r\\nown Color</strong><br>\\r\\n                                    <span class=3D=\\r\\n\\\"bb-text-muted\\\" style=3D\\\"color: #667382;\\\">(Color: Blue, Size: L)</span>\\r\\n =\\r\\n              =20\\r\\n                            </td>\\r\\n            <td class=\\r\\n=3D\\\"bb-text-center\\\" style=3D\\\"font-family: Inter, -apple-system, BlinkMacSys=\\r\\ntemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-=\\r\\nalign: center; padding: 4px 0;\\\">x 1</td>\\r\\n            <td class=3D\\\"bb-text=\\r\\n-right\\\" style=3D\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San=\\r\\n Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right=\\r\\n; padding: 4px 0; padding-right: 0;\\\">SAR1,294.00</td>\\r\\n        </tr>\\r\\n   =\\r\\n=20\\r\\n                                    <tr>\\r\\n                    <td cols=\\r\\npan=3D\\\"2\\\" class=3D\\\"bb-border-top bb-text-right\\\" style=3D\\\"font-family: Inter=\\r\\n, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helve=\\r\\ntica Neue, sans-serif; text-align: right; border-top: 1px solid #dce0e5; pa=\\r\\ndding: 4px 0; padding-left: 0;\\\">Subtotal</td>\\r\\n                    <td col=\\r\\nspan=3D\\\"2\\\" class=3D\\\"bb-border-top bb-text-right\\\" style=3D\\\"font-family: Inte=\\r\\nr, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helv=\\r\\netica Neue, sans-serif; text-align: right; border-top: 1px solid #dce0e5; p=\\r\\nadding: 4px 0; padding-right: 0;\\\">SAR1,294.00</td>\\r\\n                </tr>=\\r\\n\\r\\n           =20\\r\\n           =20\\r\\n                            <tr>\\r\\n       =\\r\\n             <td colspan=3D\\\"2\\\" class=3D\\\"bb-text-right\\\" style=3D\\\"font-family=\\r\\n: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto=\\r\\n, Helvetica Neue, sans-serif; text-align: right; padding: 4px 0; padding-le=\\r\\nft: 0;\\\">Tax</td>\\r\\n                    <td colspan=3D\\\"2\\\" class=3D\\\"bb-text-r=\\r\\night\\\" style=3D\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San F=\\r\\nrancisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right; =\\r\\npadding: 4px 0; padding-right: 0;\\\">SAR129.40</td>\\r\\n                </tr>=\\r\\n\\r\\n           =20\\r\\n           =20\\r\\n                        <tr>\\r\\n           =\\r\\n     <td colspan=3D\\\"2\\\" class=3D\\\"bb-text-right bb-font-strong bb-h3 bb-m-0\\\" =\\r\\nstyle=3D\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francis=\\r\\nco, Segoe UI, Roboto, Helvetica Neue, sans-serif; color: #232b42; font-size=\\r\\n: 20px; line-height: 130%; text-align: right; font-weight: 600; margin: 0; =\\r\\npadding: 4px 0; padding-left: 0;\\\">Total</td>\\r\\n                <td colspan=\\r\\n=3D\\\"2\\\" class=3D\\\"bb-font-strong bb-h3 bb-m-0 bb-text-right\\\" style=3D\\\"font-fa=\\r\\nmily: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Ro=\\r\\nboto, Helvetica Neue, sans-serif; color: #232b42; font-size: 20px; line-hei=\\r\\nght: 130%; text-align: right; font-weight: 600; margin: 0; padding: 4px 0; =\\r\\npadding-right: 0;\\\">SAR1,423.40</td>\\r\\n            </tr>\\r\\n            </tbo=\\r\\ndy>\\r\\n</table>\\r\\n\\r\\n\\r\\n                                    </td>\\r\\n        =\\r\\n    </tr>\\r\\n            <tr>\\r\\n                <td class=3D\\\"bb-content bb-b=\\r\\norder-top\\\" style=3D\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, =\\r\\nSan Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 40px =\\r\\n48px; border-top: 1px solid #dce0e5;\\\">\\r\\n                    <table class=\\r\\n=3D\\\"bb-row bb-mb-md\\\" cellpadding=3D\\\"0\\\" cellspacing=3D\\\"0\\\" style=3D\\\"font-fami=\\r\\nly: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Robo=\\r\\nto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; tab=\\r\\nle-layout: fixed; margin-bottom: 16px; -premailer-cellpadding: 0; -premaile=\\r\\nr-cellspacing: 0;\\\">\\r\\n                        <tbody>\\r\\n                   =\\r\\n         <tr>\\r\\n                                <td class=3D\\\"bb-bb-col\\\" sty=\\r\\nle=3D\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco,=\\r\\n Segoe UI, Roboto, Helvetica Neue, sans-serif;\\\">\\r\\n                        =\\r\\n            <h4 class=3D\\\"bb-m-0\\\" style=3D\\\"font-weight: 600; color: #232b42;=\\r\\n font-size: 16px; margin: 0;\\\">Order number</h4>\\r\\n                         =\\r\\n           <div>#10000054</div>\\r\\n                                </td>\\r\\n =\\r\\n                               <td class=3D\\\"bb-col-spacer\\\" style=3D\\\"font-fa=\\r\\nmily: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Ro=\\r\\nboto, Helvetica Neue, sans-serif; vertical-align: top; width: 24px;\\\"></td>=\\r\\n\\r\\n                                <td class=3D\\\"bb-col\\\" style=3D\\\"font-famil=\\r\\ny: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Robot=\\r\\no, Helvetica Neue, sans-serif; vertical-align: top;\\\">\\r\\n                   =\\r\\n                 <h4 class=3D\\\"bb-mb-0\\\" style=3D\\\"font-weight: 600; margin: 0=\\r\\n 0 0.5em; color: #232b42; font-size: 16px; margin-bottom: 0;\\\">Order date</h=\\r\\n4>\\r\\n                                    <div>2025-08-15 17:14:38</div>\\r\\n =\\r\\n                               </td>\\r\\n                            </tr>\\r\\n=\\r\\n                        </tbody>\\r\\n                    </table>\\r\\n         =\\r\\n           <table class=3D\\\"bb-row\\\" cellpadding=3D\\\"0\\\" cellspacing=3D\\\"0\\\" styl=\\r\\ne=3D\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, =\\r\\nSegoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; wi=\\r\\ndth: 100%; table-layout: fixed; -premailer-cellpadding: 0; -premailer-cells=\\r\\npacing: 0;\\\">\\r\\n                        <tbody>\\r\\n                          =\\r\\n  <tr>\\r\\n                                <td class=3D\\\"bb-col\\\" style=3D\\\"font=\\r\\n-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI,=\\r\\n Roboto, Helvetica Neue, sans-serif; vertical-align: top;\\\">\\r\\n             =\\r\\n                                                           <h4 class=3D\\\"bb-=\\r\\nm-0\\\" style=3D\\\"font-weight: 600; color: #232b42; font-size: 16px; margin: 0;=\\r\\n\\\">Shipping Method</h4>\\r\\n                                    <div>\\r\\n      =\\r\\n                                  Free delivery\\r\\n                         =\\r\\n           </div>\\r\\n                                                       =\\r\\n             </td>\\r\\n\\r\\n                                <td class=3D\\\"bb-col=\\r\\n-spacer\\\" style=3D\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, Sa=\\r\\nn Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; vertical-align: =\\r\\ntop; width: 24px;\\\"></td>\\r\\n                                <td class=3D\\\"bb-=\\r\\ncol\\\" style=3D\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Fr=\\r\\nancisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; vertical-align: top;=\\r\\n\\\">\\r\\n                                    <h4 class=3D\\\"bb-m-0\\\" style=3D\\\"font=\\r\\n-weight: 600; color: #232b42; font-size: 16px; margin: 0;\\\">Payment Method</=\\r\\nh4>\\r\\n                                    <div>\\r\\n                         =\\r\\n               HyperPay\\r\\n                                    </div>\\r\\n    =\\r\\n                            </td>\\r\\n                            </tr>\\r\\n   =\\r\\n                     </tbody>\\r\\n                    </table>\\r\\n            =\\r\\n    </td>\\r\\n            </tr>\\r\\n        </tbody>\\r\\n    </table>\\r\\n</div>\\r\\n=\\r\\n\\r\\n<table cellspacing=3D\\\"0\\\" cellpadding=3D\\\"0\\\" style=3D\\\"font-family: Inter, =\\r\\n-apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helveti=\\r\\nca Neue, sans-serif; border-collapse: collapse; width: 100%; -premailer-cel=\\r\\nlpadding: 0; -premailer-cellspacing: 0;\\\">\\r\\n    <tbody>\\r\\n        <tr>\\r\\n  =\\r\\n          <td class=3D\\\"bb-py-xl\\\" style=3D\\\"font-family: Inter, -apple-system=\\r\\n, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans=\\r\\n-serif; padding-top: 48px; padding-bottom: 48px;\\\">\\r\\n                <table=\\r\\n class=3D\\\"bb-text-center bb-text-muted\\\" cellspacing=3D\\\"0\\\" cellpadding=3D\\\"0\\\"=\\r\\n style=3D\\\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Franci=\\r\\nsco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collaps=\\r\\ne; width: 100%; color: #667382; text-align: center; -premailer-cellpadding:=\\r\\n 0; -premailer-cellspacing: 0;\\\">\\r\\n                    <tbody>\\r\\n          =\\r\\n         =20\\r\\n                    <tr>\\r\\n                        <td class=\\r\\n=3D\\\"bb-px-lg\\\" style=3D\\\"font-family: Inter, -apple-system, BlinkMacSystemFon=\\r\\nt, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding-rig=\\r\\nht: 24px; padding-left: 24px;\\\">\\r\\n                            =C2=A9 2025 M=\\r\\nartFury. All Rights Reserved.\\r\\n                        </td>\\r\\n           =\\r\\n         </tr>\\r\\n\\r\\n                                            <tr>\\r\\n    =\\r\\n                        <td class=3D\\\"bb-pt-md\\\" style=3D\\\"font-family: Inter,=\\r\\n -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvet=\\r\\nica Neue, sans-serif; padding-top: 16px;\\\">\\r\\n                              =\\r\\n  If you have any questions, feel free to message us at <a href=3D\\\"mailto:d=\\r\\<EMAIL>\\\" style=3D\\\"color: #206bc4; text-decoration: none;\\\">demo@exam=\\r\\nple.com</a>.\\r\\n                            </td>\\r\\n                        =\\r\\n</tr>\\r\\n                                        </tbody>\\r\\n                =\\r\\n</table>\\r\\n            </td>\\r\\n        </tr>\\r\\n    </tbody>\\r\\n</table>\\r\\n</=\\r\\ntd>\\r\\n</tr>\\r\\n</tbody>\\r\\n</table>\\r\\n</td>\\r\\n</tr>\\r\\n</tbody>\\r\\n</table>\\r\\n<=\\r\\n/center>\\r\\n</body>\\r\\n\\r\\n</html>', '', '2025-08-15 17:17:31', '2025-08-15 17:17:31')", "type": "query", "params": [], "bindings": ["\"Example\" <<EMAIL>>", "<EMAIL>", "", "", "New order(s) at MartFury", "<!doctype html>\n<html lang=\"en\">\n\n<head>\n    <meta charset=\"UTF-8\">\n    <title>MartFury</title>\n</head>\n\n<body class=\"bb-bg-body\" dir=\"ltr\" style=\"margin: 0; padding: 0; font-size: 14px; line-height: 171.4285714286%; mso-line-height-rule: exactly; color: #3A4859; width: 100%; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale; -webkit-font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11'; font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11'; font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; background-color: #f6f7f9;\">\n\n<center>\n    <table class=\"bb-main bb-bg-body\" width=\"100%\" cellspacing=\"0\" cellpadding=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; background-color: #f6f7f9;\">\n        <tbody>\n            <tr>\n                <td align=\"center\" valign=\"top\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;\">\n                    <table class=\"bb-wrap\" cellspacing=\"0\" cellpadding=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; max-width: 640px; text-align: left; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\n                        <tbody>\n                            <tr>\n                                <td class=\"bb-p-sm\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 8px;\">\n                                    <table cellpadding=\"0\" cellspacing=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\n                                        <tbody>\n                                            <tr>\n                                                <td class=\"bb-py-lg\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding-top: 24px; padding-bottom: 24px;\">\n                                                    <table cellspacing=\"0\" cellpadding=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\n                                                        <tbody>\n                                                            <tr>\n                                                                <td class=\"bb-text-left\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: left;\">\n                                                                    <a href=\"https://martfury.gc\" style=\"color: #206bc4; text-decoration: none;\">\n                                                                        <img class=\"bb-logo\" src=\"https://martfury.gc/storage/general/logo.png\" alt=\"MartFury\" style=\"line-height: 100%; outline: none; text-decoration: none; vertical-align: baseline; font-size: 0; border: 0 none; max-height: 40px;\">\n                                                                    </a>\n                                                                </td>\n                                                                <td class=\"bb-text-right\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right;\">\n                                                                    2025-08-15 17:17:31\n                                                                </td>\n                                                            </tr>\n                                                        </tbody>\n                                                    </table>\n                                                </td>\n                                            </tr>\n                                        </tbody>\n                                    </table>\n\n\n<div class=\"bb-main-content\">\n    <table class=\"bb-box\" cellpadding=\"0\" cellspacing=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; background: #ffffff; border-radius: 4px; -webkit-box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05); box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05); border: 1px solid #dce0e5; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\n        <tbody>\n            <tr>\n                <td class=\"bb-content bb-pb-0\" align=\"center\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 40px 48px; padding-bottom: 0;\">\n                    <table class=\"bb-icon bb-icon-lg bb-bg-blue\" cellspacing=\"0\" cellpadding=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 0; border-radius: 50%; background: #edeef0; line-height: 100%; font-weight: 300; border-collapse: separate; text-align: center; width: 72px; height: 72px; font-size: 48px; background-color: #206bc4; color: #ffffff; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\n                        <tbody>\n                            <tr>\n                                <td valign=\"middle\" align=\"center\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;\">\n                                    <img src=\"https://martfury.gc/vendor/core/core/base/images/email-icons/shopping-cart.png\" class=\"bb-va-middle\" width=\"40\" height=\"40\" alt=\"Icon\" style=\"border: 0 none; line-height: 100%; outline: none; text-decoration: none; font-size: 0; vertical-align: middle; display: block; width: 40px; height: 40px;\">\n                                </td>\n                            </tr>\n                        </tbody>\n                    </table>\n                    <h1 class=\"bb-text-center bb-m-0 bb-mt-md\" style=\"font-weight: 600; color: #232b42; font-size: 28px; line-height: 130%; text-align: center; margin: 0; margin-top: 16px;\">Order successfully!</h1>\n                </td>\n            </tr>\n            <tr>\n                <td class=\"bb-content\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 40px 48px;\">\n                    <div>Dear Global Office,</div>\n                    <div>You got a new order on MartFury!</div>\n                </td>\n            </tr>\n            <tr>\n                <td class=\"bb-content bb-pt-0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 40px 48px; padding-top: 0;\">\n                    <table class=\"bb-row bb-mb-md\" cellpadding=\"0\" cellspacing=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; table-layout: fixed; margin-bottom: 16px; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\n                        <tbody>\n                            <tr>\n                                <td class=\"bb-bb-col\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;\">\n                                    <h4 class=\"bb-m-0\" style=\"font-weight: 600; color: #232b42; font-size: 16px; margin: 0;\">Customer Information</h4>\n                                    <div>Name: <strong style=\"font-weight: 600;\">Fae Koelpin</strong>\n</div>\n                                                                        <div>Phone: <strong style=\"font-weight: 600;\">+18598017656</strong>\n</div>\n                                                                                                                                                <div>Address: <strong style=\"font-weight: 600;\">2358 Jadon Stream, Bashirianfurt, Utah, PE</strong>\n</div>\n                                                                    </td>\n                            </tr>\n                        </tbody>\n                    </table>\n                </td>\n            </tr>\n            <tr>\n                <td class=\"bb-content bb-pt-0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 40px 48px; padding-top: 0;\">\n                    <h4 style=\"font-weight: 600; margin: 0 0 0.5em; color: #232b42; font-size: 16px;\">Here's what you ordered:</h4>\n                    <a class=\"button button-blue\" href=\"https://martfury.gc/orders/tracking?order_id=%2310000054&amp;email=vendor%40botble.com\" style=\"color: #206bc4; text-decoration: none;\">View order</a>\n    or <a href=\"https://martfury.gc\" style=\"color: #206bc4; text-decoration: none;\">Go to our shop</a>\n\n    <br>\n\n<table class=\"bb-table\" cellspacing=\"0\" cellpadding=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\n    <thead>\n        <tr>\n            <th colspan=\"2\" style=\"text-transform: uppercase; font-weight: 600; color: #667382; font-size: 12px; padding: 0 0 4px 0; padding-left: 0;\"></th>\n            <th style=\"text-transform: uppercase; font-weight: 600; color: #667382; font-size: 12px; padding: 0 0 4px 0;\">Quantity</th>\n            <th class=\"bb-text-right\" style=\"text-align: right; text-transform: uppercase; font-weight: 600; color: #667382; font-size: 12px; padding: 0 0 4px 0; padding-right: 0;\">Price</th>\n        </tr>\n    </thead>\n\n    <tbody>\n                <tr>\n            <td class=\"bb-pr-0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding-right: 0; padding: 4px 0; padding-left: 0;\">\n                <a href=\"\" style=\"color: #206bc4; text-decoration: none;\">\n                    <img src=\"https://martfury.gc/storage/products/10-150x150.jpg\" class=\" bb-rounded\" width=\"64\" height=\"64\" alt=\"\" style=\"line-height: 100%; outline: none; text-decoration: none; vertical-align: baseline; font-size: 0; border: 0 none; border-radius: 4px;\">\n                </a>\n            </td>\n            <td class=\"bb-pl-md bb-w-100p\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; width: 100%; padding-left: 16px !important; padding: 4px 0;\">\n                <strong style=\"font-weight: 600;\">Herschel Leather Duffle Bag In Brown Color</strong><br>\n                                    <span class=\"bb-text-muted\" style=\"color: #667382;\">(Color: Blue, Size: L)</span>\n                \n                            </td>\n            <td class=\"bb-text-center\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: center; padding: 4px 0;\">x 1</td>\n            <td class=\"bb-text-right\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right; padding: 4px 0; padding-right: 0;\">SAR1,294.00</td>\n        </tr>\n    \n                                    <tr>\n                    <td colspan=\"2\" class=\"bb-border-top bb-text-right\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right; border-top: 1px solid #dce0e5; padding: 4px 0; padding-left: 0;\">Subtotal</td>\n                    <td colspan=\"2\" class=\"bb-border-top bb-text-right\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right; border-top: 1px solid #dce0e5; padding: 4px 0; padding-right: 0;\">SAR1,294.00</td>\n                </tr>\n            \n            \n                            <tr>\n                    <td colspan=\"2\" class=\"bb-text-right\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right; padding: 4px 0; padding-left: 0;\">Tax</td>\n                    <td colspan=\"2\" class=\"bb-text-right\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right; padding: 4px 0; padding-right: 0;\">SAR129.40</td>\n                </tr>\n            \n            \n                        <tr>\n                <td colspan=\"2\" class=\"bb-text-right bb-font-strong bb-h3 bb-m-0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; color: #232b42; font-size: 20px; line-height: 130%; text-align: right; font-weight: 600; margin: 0; padding: 4px 0; padding-left: 0;\">Total</td>\n                <td colspan=\"2\" class=\"bb-font-strong bb-h3 bb-m-0 bb-text-right\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; color: #232b42; font-size: 20px; line-height: 130%; text-align: right; font-weight: 600; margin: 0; padding: 4px 0; padding-right: 0;\">SAR1,423.40</td>\n            </tr>\n            </tbody>\n</table>\n\n\n                                    </td>\n            </tr>\n            <tr>\n                <td class=\"bb-content bb-border-top\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 40px 48px; border-top: 1px solid #dce0e5;\">\n                    <table class=\"bb-row bb-mb-md\" cellpadding=\"0\" cellspacing=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; table-layout: fixed; margin-bottom: 16px; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\n                        <tbody>\n                            <tr>\n                                <td class=\"bb-bb-col\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;\">\n                                    <h4 class=\"bb-m-0\" style=\"font-weight: 600; color: #232b42; font-size: 16px; margin: 0;\">Order number</h4>\n                                    <div>#10000054</div>\n                                </td>\n                                <td class=\"bb-col-spacer\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; vertical-align: top; width: 24px;\"></td>\n                                <td class=\"bb-col\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; vertical-align: top;\">\n                                    <h4 class=\"bb-mb-0\" style=\"font-weight: 600; margin: 0 0 0.5em; color: #232b42; font-size: 16px; margin-bottom: 0;\">Order date</h4>\n                                    <div>2025-08-15 17:14:38</div>\n                                </td>\n                            </tr>\n                        </tbody>\n                    </table>\n                    <table class=\"bb-row\" cellpadding=\"0\" cellspacing=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; table-layout: fixed; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\n                        <tbody>\n                            <tr>\n                                <td class=\"bb-col\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; vertical-align: top;\">\n                                                                        <h4 class=\"bb-m-0\" style=\"font-weight: 600; color: #232b42; font-size: 16px; margin: 0;\">Shipping Method</h4>\n                                    <div>\n                                        Free delivery\n                                    </div>\n                                                                    </td>\n\n                                <td class=\"bb-col-spacer\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; vertical-align: top; width: 24px;\"></td>\n                                <td class=\"bb-col\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; vertical-align: top;\">\n                                    <h4 class=\"bb-m-0\" style=\"font-weight: 600; color: #232b42; font-size: 16px; margin: 0;\">Payment Method</h4>\n                                    <div>\n                                        HyperPay\n                                    </div>\n                                </td>\n                            </tr>\n                        </tbody>\n                    </table>\n                </td>\n            </tr>\n        </tbody>\n    </table>\n</div>\n\n<table cellspacing=\"0\" cellpadding=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\n    <tbody>\n        <tr>\n            <td class=\"bb-py-xl\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding-top: 48px; padding-bottom: 48px;\">\n                <table class=\"bb-text-center bb-text-muted\" cellspacing=\"0\" cellpadding=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; color: #667382; text-align: center; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\n                    <tbody>\n                    \n                    <tr>\n                        <td class=\"bb-px-lg\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding-right: 24px; padding-left: 24px;\">\n                            © 2025 MartFury. All Rights Reserved.\n                        </td>\n                    </tr>\n\n                                            <tr>\n                            <td class=\"bb-pt-md\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding-top: 16px;\">\n                                If you have any questions, feel free to message us at <a href=\"mailto:<EMAIL>\" style=\"color: #206bc4; text-decoration: none;\"><EMAIL></a>.\n                            </td>\n                        </tr>\n                                        </tbody>\n                </table>\n            </td>\n        </tr>\n    </tbody>\n</table>\n</td>\n</tr>\n</tbody>\n</table>\n</td>\n</tr>\n</tbody>\n</table>\n</center>\n</body>\n\n</html>", null, "From: Example <<EMAIL>>\r\nTo: <EMAIL>\r\nSubject: New order(s) at MartFury\r\nMessage-ID: <<EMAIL>>\r\nMIME-Version: 1.0\r\nDate: Fri, 15 Aug 2025 17:17:31 +0000\r\nContent-Type: text/html; charset=utf-8\r\nContent-Transfer-Encoding: quoted-printable\r\n\r\n<!doctype html>\r\n<html lang=3D\"en\">\r\n\r\n<head>\r\n    <meta charset=3D\"UTF=\r\n-8\">\r\n    <title>MartFury</title>\r\n</head>\r\n\r\n<body class=3D\"bb-bg-body=\r\n\" dir=3D\"ltr\" style=3D\"margin: 0; padding: 0; font-size: 14px; line-height:=\r\n 171.4285714286%; mso-line-height-rule: exactly; color: #3A4859; width: 100=\r\n%; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale;=\r\n -webkit-font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11'; font-featur=\r\ne-settings: 'cv02', 'cv03', 'cv04', 'cv11'; font-family: Inter, -apple-syst=\r\nem, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sa=\r\nns-serif; background-color: #f6f7f9;\">\r\n\r\n<center>\r\n    <table class=3D\"=\r\nbb-main bb-bg-body\" width=3D\"100%\" cellspacing=3D\"0\" cellpadding=3D\"0\" styl=\r\ne=3D\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, =\r\nSegoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; wi=\r\ndth: 100%; -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; back=\r\nground-color: #f6f7f9;\">\r\n        <tbody>\r\n            <tr>\r\n           =\r\n     <td align=3D\"center\" valign=3D\"top\" style=3D\"font-family: Inter, -appl=\r\ne-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Ne=\r\nue, sans-serif;\">\r\n                    <table class=3D\"bb-wrap\" cellspacin=\r\ng=3D\"0\" cellpadding=3D\"0\" style=3D\"font-family: Inter, -apple-system, Blink=\r\nMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;=\r\n border-collapse: collapse; width: 100%; max-width: 640px; text-align: left=\r\n; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\r\n               =\r\n         <tbody>\r\n                            <tr>\r\n                     =\r\n           <td class=3D\"bb-p-sm\" style=3D\"font-family: Inter, -apple-system=\r\n, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans=\r\n-serif; padding: 8px;\">\r\n                                    <table cellpa=\r\ndding=3D\"0\" cellspacing=3D\"0\" style=3D\"font-family: Inter, -apple-system, B=\r\nlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-se=\r\nrif; border-collapse: collapse; width: 100%; -premailer-cellpadding: 0; -pr=\r\nemailer-cellspacing: 0;\">\r\n                                        <tbody>=\r\n\r\n                                            <tr>\r\n                     =\r\n                           <td class=3D\"bb-py-lg\" style=3D\"font-family: Int=\r\ner, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Hel=\r\nvetica Neue, sans-serif; padding-top: 24px; padding-bottom: 24px;\">\r\n     =\r\n                                               <table cellspacing=3D\"0\" cel=\r\nlpadding=3D\"0\" style=3D\"font-family: Inter, -apple-system, BlinkMacSystemFo=\r\nnt, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-col=\r\nlapse: collapse; width: 100%; -premailer-cellpadding: 0; -premailer-cellspa=\r\ncing: 0;\">\r\n                                                        <tbody=\r\n>\r\n                                                            <tr>\r\n    =\r\n                                                            <td class=3D\"bb=\r\n-text-left\" style=3D\"font-family: Inter, -apple-system, BlinkMacSystemFont,=\r\n San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: l=\r\neft;\">\r\n                                                                  =\r\n  <a href=3D\"https://martfury.gc\" style=3D\"color: #206bc4; text-decoration:=\r\n none;\">\r\n                                                                =\r\n        <img class=3D\"bb-logo\" src=3D\"https://martfury.gc/storage/general/l=\r\nogo.png\" alt=3D\"MartFury\" style=3D\"line-height: 100%; outline: none; text-d=\r\necoration: none; vertical-align: baseline; font-size: 0; border: 0 none; ma=\r\nx-height: 40px;\">\r\n                                                       =\r\n             </a>\r\n                                                       =\r\n         </td>\r\n                                                          =\r\n      <td class=3D\"bb-text-right\" style=3D\"font-family: Inter, -apple-syste=\r\nm, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, san=\r\ns-serif; text-align: right;\">\r\n                                           =\r\n                         2025-08-15 17:17:31\r\n                            =\r\n                                    </td>\r\n                               =\r\n                             </tr>\r\n                                      =\r\n                  </tbody>\r\n                                              =\r\n      </table>\r\n                                                </td>\r\n  =\r\n                                          </tr>\r\n                         =\r\n               </tbody>\r\n                                    </table>\r\n=\r\n\r\n\r\n<div class=3D\"bb-main-content\">\r\n    <table class=3D\"bb-box\" cellpad=\r\nding=3D\"0\" cellspacing=3D\"0\" style=3D\"font-family: Inter, -apple-system, Bl=\r\ninkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-ser=\r\nif; border-collapse: collapse; width: 100%; background: #ffffff; border-rad=\r\nius: 4px; -webkit-box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05); box-shadow: 0 =\r\n1px 4px rgba(0, 0, 0, 0.05); border: 1px solid #dce0e5; -premailer-cellpadd=\r\ning: 0; -premailer-cellspacing: 0;\">\r\n        <tbody>\r\n            <tr>=\r\n\r\n                <td class=3D\"bb-content bb-pb-0\" align=3D\"center\" style=\r\n=3D\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, S=\r\negoe UI, Roboto, Helvetica Neue, sans-serif; padding: 40px 48px; padding-bo=\r\nttom: 0;\">\r\n                    <table class=3D\"bb-icon bb-icon-lg bb-bg-b=\r\nlue\" cellspacing=3D\"0\" cellpadding=3D\"0\" style=3D\"font-family: Inter, -appl=\r\ne-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Ne=\r\nue, sans-serif; padding: 0; border-radius: 50%; background: #edeef0; line-h=\r\neight: 100%; font-weight: 300; border-collapse: separate; text-align: cente=\r\nr; width: 72px; height: 72px; font-size: 48px; background-color: #206bc4; c=\r\nolor: #ffffff; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\r\n  =\r\n                      <tbody>\r\n                            <tr>\r\n        =\r\n                        <td valign=3D\"middle\" align=3D\"center\" style=3D\"fon=\r\nt-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI=\r\n, Roboto, Helvetica Neue, sans-serif;\">\r\n                                 =\r\n   <img src=3D\"https://martfury.gc/vendor/core/core/base/images/email-icons=\r\n/shopping-cart.png\" class=3D\"bb-va-middle\" width=3D\"40\" height=3D\"40\" alt=\r\n=3D\"Icon\" style=3D\"border: 0 none; line-height: 100%; outline: none; text-d=\r\necoration: none; font-size: 0; vertical-align: middle; display: block; widt=\r\nh: 40px; height: 40px;\">\r\n                                </td>\r\n        =\r\n                    </tr>\r\n                        </tbody>\r\n            =\r\n        </table>\r\n                    <h1 class=3D\"bb-text-center bb-m-0 b=\r\nb-mt-md\" style=3D\"font-weight: 600; color: #232b42; font-size: 28px; line-h=\r\neight: 130%; text-align: center; margin: 0; margin-top: 16px;\">Order succes=\r\nsfully!</h1>\r\n                </td>\r\n            </tr>\r\n            <tr>=\r\n\r\n                <td class=3D\"bb-content\" style=3D\"font-family: Inter, -a=\r\npple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica=\r\n Neue, sans-serif; padding: 40px 48px;\">\r\n                    <div>Dear Gl=\r\nobal Office,</div>\r\n                    <div>You got a new order on MartFu=\r\nry!</div>\r\n                </td>\r\n            </tr>\r\n            <tr>\r\n=\r\n                <td class=3D\"bb-content bb-pt-0\" style=3D\"font-family: Inte=\r\nr, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helv=\r\netica Neue, sans-serif; padding: 40px 48px; padding-top: 0;\">\r\n           =\r\n         <table class=3D\"bb-row bb-mb-md\" cellpadding=3D\"0\" cellspacing=3D\"=\r\n0\" style=3D\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Fran=\r\ncisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: colla=\r\npse; width: 100%; table-layout: fixed; margin-bottom: 16px; -premailer-cell=\r\npadding: 0; -premailer-cellspacing: 0;\">\r\n                        <tbody>=\r\n\r\n                            <tr>\r\n                                <td c=\r\nlass=3D\"bb-bb-col\" style=3D\"font-family: Inter, -apple-system, BlinkMacSyst=\r\nemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;\">\r\n  =\r\n                                  <h4 class=3D\"bb-m-0\" style=3D\"font-weight=\r\n: 600; color: #232b42; font-size: 16px; margin: 0;\">Customer Information</h=\r\n4>\r\n                                    <div>Name: <strong style=3D\"font-w=\r\neight: 600;\">Fae Koelpin</strong>\r\n</div>\r\n                              =\r\n                                          <div>Phone: <strong style=3D\"font=\r\n-weight: 600;\">+18598017656</strong>\r\n</div>\r\n                           =\r\n                                                                           =\r\n                                          <div>Address: <strong style=3D\"fo=\r\nnt-weight: 600;\">2358 Jadon Stream, Bashirianfurt, Utah, PE</strong>\r\n</di=\r\nv>\r\n                                                                    </=\r\ntd>\r\n                            </tr>\r\n                        </tbody>=\r\n\r\n                    </table>\r\n                </td>\r\n            </tr>=\r\n\r\n            <tr>\r\n                <td class=3D\"bb-content bb-pt-0\" styl=\r\ne=3D\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, =\r\nSegoe UI, Roboto, Helvetica Neue, sans-serif; padding: 40px 48px; padding-t=\r\nop: 0;\">\r\n                    <h4 style=3D\"font-weight: 600; margin: 0 0 0=\r\n.5em; color: #232b42; font-size: 16px;\">Here's what you ordered:</h4>\r\n   =\r\n                 <a class=3D\"button button-blue\" href=3D\"https://martfury.g=\r\nc/orders/tracking?order_id=3D%2310000054&amp;email=3Dvendor%40botble.com\" s=\r\ntyle=3D\"color: #206bc4; text-decoration: none;\">View order</a>\r\n    or <a =\r\nhref=3D\"https://martfury.gc\" style=3D\"color: #206bc4; text-decoration: none=\r\n;\">Go to our shop</a>\r\n\r\n    <br>\r\n\r\n<table class=3D\"bb-table\" cellspac=\r\ning=3D\"0\" cellpadding=3D\"0\" style=3D\"font-family: Inter, -apple-system, Bli=\r\nnkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-seri=\r\nf; border-collapse: collapse; width: 100%; -premailer-cellpadding: 0; -prem=\r\nailer-cellspacing: 0;\">\r\n    <thead>\r\n        <tr>\r\n            <th cols=\r\npan=3D\"2\" style=3D\"text-transform: uppercase; font-weight: 600; color: #667=\r\n382; font-size: 12px; padding: 0 0 4px 0; padding-left: 0;\"></th>\r\n       =\r\n     <th style=3D\"text-transform: uppercase; font-weight: 600; color: #6673=\r\n82; font-size: 12px; padding: 0 0 4px 0;\">Quantity</th>\r\n            <th c=\r\nlass=3D\"bb-text-right\" style=3D\"text-align: right; text-transform: uppercas=\r\ne; font-weight: 600; color: #667382; font-size: 12px; padding: 0 0 4px 0; p=\r\nadding-right: 0;\">Price</th>\r\n        </tr>\r\n    </thead>\r\n\r\n    <tbody=\r\n>\r\n                <tr>\r\n            <td class=3D\"bb-pr-0\" style=3D\"font-=\r\nfamily: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, =\r\nRoboto, Helvetica Neue, sans-serif; padding-right: 0; padding: 4px 0; paddi=\r\nng-left: 0;\">\r\n                <a href=3D\"\" style=3D\"color: #206bc4; text-=\r\ndecoration: none;\">\r\n                    <img src=3D\"https://martfury.gc/s=\r\ntorage/products/10-150x150.jpg\" class=3D\" bb-rounded\" width=3D\"64\" height=\r\n=3D\"64\" alt=3D\"\" style=3D\"line-height: 100%; outline: none; text-decoration=\r\n: none; vertical-align: baseline; font-size: 0; border: 0 none; border-radi=\r\nus: 4px;\">\r\n                </a>\r\n            </td>\r\n            <td cla=\r\nss=3D\"bb-pl-md bb-w-100p\" style=3D\"font-family: Inter, -apple-system, Blink=\r\nMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;=\r\n width: 100%; padding-left: 16px !important; padding: 4px 0;\">\r\n          =\r\n      <strong style=3D\"font-weight: 600;\">Herschel Leather Duffle Bag In Br=\r\nown Color</strong><br>\r\n                                    <span class=3D=\r\n\"bb-text-muted\" style=3D\"color: #667382;\">(Color: Blue, Size: L)</span>\r\n =\r\n              =20\r\n                            </td>\r\n            <td class=\r\n=3D\"bb-text-center\" style=3D\"font-family: Inter, -apple-system, BlinkMacSys=\r\ntemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-=\r\nalign: center; padding: 4px 0;\">x 1</td>\r\n            <td class=3D\"bb-text=\r\n-right\" style=3D\"font-family: Inter, -apple-system, BlinkMacSystemFont, San=\r\n Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right=\r\n; padding: 4px 0; padding-right: 0;\">SAR1,294.00</td>\r\n        </tr>\r\n   =\r\n=20\r\n                                    <tr>\r\n                    <td cols=\r\npan=3D\"2\" class=3D\"bb-border-top bb-text-right\" style=3D\"font-family: Inter=\r\n, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helve=\r\ntica Neue, sans-serif; text-align: right; border-top: 1px solid #dce0e5; pa=\r\ndding: 4px 0; padding-left: 0;\">Subtotal</td>\r\n                    <td col=\r\nspan=3D\"2\" class=3D\"bb-border-top bb-text-right\" style=3D\"font-family: Inte=\r\nr, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helv=\r\netica Neue, sans-serif; text-align: right; border-top: 1px solid #dce0e5; p=\r\nadding: 4px 0; padding-right: 0;\">SAR1,294.00</td>\r\n                </tr>=\r\n\r\n           =20\r\n           =20\r\n                            <tr>\r\n       =\r\n             <td colspan=3D\"2\" class=3D\"bb-text-right\" style=3D\"font-family=\r\n: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto=\r\n, Helvetica Neue, sans-serif; text-align: right; padding: 4px 0; padding-le=\r\nft: 0;\">Tax</td>\r\n                    <td colspan=3D\"2\" class=3D\"bb-text-r=\r\night\" style=3D\"font-family: Inter, -apple-system, BlinkMacSystemFont, San F=\r\nrancisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right; =\r\npadding: 4px 0; padding-right: 0;\">SAR129.40</td>\r\n                </tr>=\r\n\r\n           =20\r\n           =20\r\n                        <tr>\r\n           =\r\n     <td colspan=3D\"2\" class=3D\"bb-text-right bb-font-strong bb-h3 bb-m-0\" =\r\nstyle=3D\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francis=\r\nco, Segoe UI, Roboto, Helvetica Neue, sans-serif; color: #232b42; font-size=\r\n: 20px; line-height: 130%; text-align: right; font-weight: 600; margin: 0; =\r\npadding: 4px 0; padding-left: 0;\">Total</td>\r\n                <td colspan=\r\n=3D\"2\" class=3D\"bb-font-strong bb-h3 bb-m-0 bb-text-right\" style=3D\"font-fa=\r\nmily: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Ro=\r\nboto, Helvetica Neue, sans-serif; color: #232b42; font-size: 20px; line-hei=\r\nght: 130%; text-align: right; font-weight: 600; margin: 0; padding: 4px 0; =\r\npadding-right: 0;\">SAR1,423.40</td>\r\n            </tr>\r\n            </tbo=\r\ndy>\r\n</table>\r\n\r\n\r\n                                    </td>\r\n        =\r\n    </tr>\r\n            <tr>\r\n                <td class=3D\"bb-content bb-b=\r\norder-top\" style=3D\"font-family: Inter, -apple-system, BlinkMacSystemFont, =\r\nSan Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 40px =\r\n48px; border-top: 1px solid #dce0e5;\">\r\n                    <table class=\r\n=3D\"bb-row bb-mb-md\" cellpadding=3D\"0\" cellspacing=3D\"0\" style=3D\"font-fami=\r\nly: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Robo=\r\nto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; tab=\r\nle-layout: fixed; margin-bottom: 16px; -premailer-cellpadding: 0; -premaile=\r\nr-cellspacing: 0;\">\r\n                        <tbody>\r\n                   =\r\n         <tr>\r\n                                <td class=3D\"bb-bb-col\" sty=\r\nle=3D\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco,=\r\n Segoe UI, Roboto, Helvetica Neue, sans-serif;\">\r\n                        =\r\n            <h4 class=3D\"bb-m-0\" style=3D\"font-weight: 600; color: #232b42;=\r\n font-size: 16px; margin: 0;\">Order number</h4>\r\n                         =\r\n           <div>#10000054</div>\r\n                                </td>\r\n =\r\n                               <td class=3D\"bb-col-spacer\" style=3D\"font-fa=\r\nmily: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Ro=\r\nboto, Helvetica Neue, sans-serif; vertical-align: top; width: 24px;\"></td>=\r\n\r\n                                <td class=3D\"bb-col\" style=3D\"font-famil=\r\ny: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Robot=\r\no, Helvetica Neue, sans-serif; vertical-align: top;\">\r\n                   =\r\n                 <h4 class=3D\"bb-mb-0\" style=3D\"font-weight: 600; margin: 0=\r\n 0 0.5em; color: #232b42; font-size: 16px; margin-bottom: 0;\">Order date</h=\r\n4>\r\n                                    <div>2025-08-15 17:14:38</div>\r\n =\r\n                               </td>\r\n                            </tr>\r\n=\r\n                        </tbody>\r\n                    </table>\r\n         =\r\n           <table class=3D\"bb-row\" cellpadding=3D\"0\" cellspacing=3D\"0\" styl=\r\ne=3D\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, =\r\nSegoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; wi=\r\ndth: 100%; table-layout: fixed; -premailer-cellpadding: 0; -premailer-cells=\r\npacing: 0;\">\r\n                        <tbody>\r\n                          =\r\n  <tr>\r\n                                <td class=3D\"bb-col\" style=3D\"font=\r\n-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI,=\r\n Roboto, Helvetica Neue, sans-serif; vertical-align: top;\">\r\n             =\r\n                                                           <h4 class=3D\"bb-=\r\nm-0\" style=3D\"font-weight: 600; color: #232b42; font-size: 16px; margin: 0;=\r\n\">Shipping Method</h4>\r\n                                    <div>\r\n      =\r\n                                  Free delivery\r\n                         =\r\n           </div>\r\n                                                       =\r\n             </td>\r\n\r\n                                <td class=3D\"bb-col=\r\n-spacer\" style=3D\"font-family: Inter, -apple-system, BlinkMacSystemFont, Sa=\r\nn Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; vertical-align: =\r\ntop; width: 24px;\"></td>\r\n                                <td class=3D\"bb-=\r\ncol\" style=3D\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Fr=\r\nancisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; vertical-align: top;=\r\n\">\r\n                                    <h4 class=3D\"bb-m-0\" style=3D\"font=\r\n-weight: 600; color: #232b42; font-size: 16px; margin: 0;\">Payment Method</=\r\nh4>\r\n                                    <div>\r\n                         =\r\n               HyperPay\r\n                                    </div>\r\n    =\r\n                            </td>\r\n                            </tr>\r\n   =\r\n                     </tbody>\r\n                    </table>\r\n            =\r\n    </td>\r\n            </tr>\r\n        </tbody>\r\n    </table>\r\n</div>\r\n=\r\n\r\n<table cellspacing=3D\"0\" cellpadding=3D\"0\" style=3D\"font-family: Inter, =\r\n-apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helveti=\r\nca Neue, sans-serif; border-collapse: collapse; width: 100%; -premailer-cel=\r\nlpadding: 0; -premailer-cellspacing: 0;\">\r\n    <tbody>\r\n        <tr>\r\n  =\r\n          <td class=3D\"bb-py-xl\" style=3D\"font-family: Inter, -apple-system=\r\n, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans=\r\n-serif; padding-top: 48px; padding-bottom: 48px;\">\r\n                <table=\r\n class=3D\"bb-text-center bb-text-muted\" cellspacing=3D\"0\" cellpadding=3D\"0\"=\r\n style=3D\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Franci=\r\nsco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collaps=\r\ne; width: 100%; color: #667382; text-align: center; -premailer-cellpadding:=\r\n 0; -premailer-cellspacing: 0;\">\r\n                    <tbody>\r\n          =\r\n         =20\r\n                    <tr>\r\n                        <td class=\r\n=3D\"bb-px-lg\" style=3D\"font-family: Inter, -apple-system, BlinkMacSystemFon=\r\nt, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding-rig=\r\nht: 24px; padding-left: 24px;\">\r\n                            =C2=A9 2025 M=\r\nartFury. All Rights Reserved.\r\n                        </td>\r\n           =\r\n         </tr>\r\n\r\n                                            <tr>\r\n    =\r\n                        <td class=3D\"bb-pt-md\" style=3D\"font-family: Inter,=\r\n -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvet=\r\nica Neue, sans-serif; padding-top: 16px;\">\r\n                              =\r\n  If you have any questions, feel free to message us at <a href=3D\"mailto:d=\r\n<EMAIL>\" style=3D\"color: #206bc4; text-decoration: none;\">demo@exam=\r\nple.com</a>.\r\n                            </td>\r\n                        =\r\n</tr>\r\n                                        </tbody>\r\n                =\r\n</table>\r\n            </td>\r\n        </tr>\r\n    </tbody>\r\n</table>\r\n</=\r\ntd>\r\n</tr>\r\n</tbody>\r\n</table>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table>\r\n<=\r\n/center>\r\n</body>\r\n\r\n</html>", "", "2025-08-15 17:17:31", "2025-08-15 17:17:31"], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "platform/plugins/fob-email-log/src/Listeners/EmailLogger.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\fob-email-log\\src\\Listeners\\EmailLogger.php", "line": 20}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Mail/Mailer.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\Mailer.php", "line": 617}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Mail/Mailer.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\Mailer.php", "line": 336}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Mail/Mailable.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\Mailable.php", "line": 207}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Mail/Mailable.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Mail\\Mailable.php", "line": 200}], "start": **********.527169, "duration": 0.0054800000000000005, "duration_str": "5.48ms", "memory": 0, "memory_str": null, "filename": "EmailLogger.php:20", "source": {"index": 18, "namespace": null, "name": "platform/plugins/fob-email-log/src/Listeners/EmailLogger.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\fob-email-log\\src\\Listeners\\EmailLogger.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Ffob-email-log%2Fsrc%2FListeners%2FEmailLogger.php&line=20", "ajax": false, "filename": "EmailLogger.php", "line": "20"}, "connection": "martfury", "explain": null, "start_percent": 89.857, "width_percent": 4.356}, {"sql": "insert into `ec_order_histories` (`action`, `description`, `order_id`, `updated_at`, `created_at`) values ('create_order', 'New order #10000054 from <PERSON><PERSON>', 54, '2025-08-15 17:17:31', '2025-08-15 17:17:31')", "type": "query", "params": [], "bindings": [{"value": "create_order", "label": "create_order"}, "New order #10000054 from <PERSON><PERSON>", 54, "2025-08-15 17:17:31", "2025-08-15 17:17:31"], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 145}, {"index": 20, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 321}, {"index": 24, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 46}, {"index": 26, "namespace": null, "name": "platform/plugins/hyperpay/src/Http/Controllers/HyperPayController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\hyperpay\\src\\Http\\Controllers\\HyperPayController.php", "line": 84}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.536792, "duration": 0.0038799999999999998, "duration_str": "3.88ms", "memory": 0, "memory_str": null, "filename": "OrderHelper.php:145", "source": {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 145}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FSupports%2FOrderHelper.php&line=145", "ajax": false, "filename": "OrderHelper.php", "line": "145"}, "connection": "martfury", "explain": null, "start_percent": 94.213, "width_percent": 3.084}, {"sql": "select `ec_flash_sales`.*, `ec_flash_sale_products`.`product_id` as `pivot_product_id`, `ec_flash_sale_products`.`flash_sale_id` as `pivot_flash_sale_id`, `ec_flash_sale_products`.`price` as `pivot_price`, `ec_flash_sale_products`.`quantity` as `pivot_quantity`, `ec_flash_sale_products`.`sold` as `pivot_sold` from `ec_flash_sales` inner join `ec_flash_sale_products` on `ec_flash_sales`.`id` = `ec_flash_sale_products`.`flash_sale_id` where `ec_flash_sale_products`.`product_id` = 10 and `status` = 'published' and date(`end_date`) >= '2025-08-15' and `ec_flash_sale_products`.`quantity` > sold order by `created_at` desc limit 1", "type": "query", "params": [], "bindings": [10, "published", "2025-08-15"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 183}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 321}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php", "line": 46}, {"index": 24, "namespace": null, "name": "platform/plugins/hyperpay/src/Http/Controllers/HyperPayController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\hyperpay\\src\\Http\\Controllers\\HyperPayController.php", "line": 84}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.5501618, "duration": 0.0017900000000000001, "duration_str": "1.79ms", "memory": 0, "memory_str": null, "filename": "OrderHelper.php:183", "source": {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/OrderHelper.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Supports\\OrderHelper.php", "line": 183}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FSupports%2FOrderHelper.php&line=183", "ajax": false, "filename": "OrderHelper.php", "line": "183"}, "connection": "martfury", "explain": null, "start_percent": 97.297, "width_percent": 1.423}, {"sql": "select * from `ec_orders` where `ec_orders`.`id` = 54 limit 1", "type": "query", "params": [], "bindings": [54], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/plugins/hyperpay/src/Http/Controllers/HyperPayController.php", "file": "D:\\laragon\\www\\martfury\\platform\\plugins\\hyperpay\\src\\Http\\Controllers\\HyperPayController.php", "line": 100}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.5543, "duration": 0.00161, "duration_str": "1.61ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/botble/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "martfury", "explain": null, "start_percent": 98.72, "width_percent": 1.28}]}, "models": {"data": {"Botble\\Ecommerce\\Models\\Product": {"retrieved": 8, "updated": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\Order": {"retrieved": 5, "updated": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FOrder.php&line=1", "ajax": false, "filename": "Order.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\ProductVariation": {"retrieved": 6, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProductVariation.php&line=1", "ajax": false, "filename": "ProductVariation.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\Currency": {"retrieved": 5, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FCurrency.php&line=1", "ajax": false, "filename": "Currency.php", "line": "?"}}, "Botble\\Payment\\Models\\Payment": {"created": 1, "retrieved": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fpayment%2Fsrc%2FModels%2FPayment.php&line=1", "ajax": false, "filename": "Payment.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\OrderAddress": {"retrieved": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FOrderAddress.php&line=1", "ajax": false, "filename": "OrderAddress.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\OrderProduct": {"retrieved": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FOrderProduct.php&line=1", "ajax": false, "filename": "OrderProduct.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\InvoiceItem": {"created": 1, "retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FInvoiceItem.php&line=1", "ajax": false, "filename": "InvoiceItem.php", "line": "?"}}, "Botble\\Language\\Models\\Language": {"retrieved": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Flanguage%2Fsrc%2FModels%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}, "Botble\\Marketplace\\Models\\Store": {"retrieved": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FModels%2FStore.php&line=1", "ajax": false, "filename": "Store.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\Customer": {"retrieved": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\ShippingRule": {"retrieved": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FShippingRule.php&line=1", "ajax": false, "filename": "ShippingRule.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\OrderHistory": {"created": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FOrderHistory.php&line=1", "ajax": false, "filename": "OrderHistory.php", "line": "?"}}, "Botble\\ACL\\Models\\User": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Facl%2Fsrc%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\Invoice": {"created": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FInvoice.php&line=1", "ajax": false, "filename": "Invoice.php", "line": "?"}}, "Botble\\Base\\Models\\AdminNotification": {"created": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fplatform%2Fbase%2Fsrc%2FModels%2FAdminNotification.php&line=1", "ajax": false, "filename": "AdminNotification.php", "line": "?"}}, "Botble\\Slug\\Models\\Slug": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fvendor%2Fbotble%2Fslug%2Fsrc%2FModels%2FSlug.php&line=1", "ajax": false, "filename": "Slug.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\Shipment": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FShipment.php&line=1", "ajax": false, "filename": "Shipment.php", "line": "?"}}, "FriendsOfBotble\\EmailLog\\Models\\EmailLog": {"created": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Ffob-email-log%2Fsrc%2FModels%2FEmailLog.php&line=1", "ajax": false, "filename": "EmailLog.php", "line": "?"}}}, "count": 57, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 46, "created": 7, "updated": 4}}, "symfonymailer_mails": {"count": 1, "mails": [{"to": ["<EMAIL>"], "subject": "New order(s) at MartFury", "headers": "From: Example <<EMAIL>>\r\nTo: <EMAIL>\r\nSubject: New order(s) at MartFury\r\n", "body": null, "html": "<!doctype html>\n<html lang=\"en\">\n\n<head>\n    <meta charset=\"UTF-8\">\n    <title>MartFury</title>\n</head>\n\n<body class=\"bb-bg-body\" dir=\"ltr\" style=\"margin: 0; padding: 0; font-size: 14px; line-height: 171.4285714286%; mso-line-height-rule: exactly; color: #3A4859; width: 100%; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale; -webkit-font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11'; font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11'; font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; background-color: #f6f7f9;\">\n\n<center>\n    <table class=\"bb-main bb-bg-body\" width=\"100%\" cellspacing=\"0\" cellpadding=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; background-color: #f6f7f9;\">\n        <tbody>\n            <tr>\n                <td align=\"center\" valign=\"top\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;\">\n                    <table class=\"bb-wrap\" cellspacing=\"0\" cellpadding=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; max-width: 640px; text-align: left; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\n                        <tbody>\n                            <tr>\n                                <td class=\"bb-p-sm\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 8px;\">\n                                    <table cellpadding=\"0\" cellspacing=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\n                                        <tbody>\n                                            <tr>\n                                                <td class=\"bb-py-lg\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding-top: 24px; padding-bottom: 24px;\">\n                                                    <table cellspacing=\"0\" cellpadding=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\n                                                        <tbody>\n                                                            <tr>\n                                                                <td class=\"bb-text-left\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: left;\">\n                                                                    <a href=\"https://martfury.gc\" style=\"color: #206bc4; text-decoration: none;\">\n                                                                        <img class=\"bb-logo\" src=\"https://martfury.gc/storage/general/logo.png\" alt=\"MartFury\" style=\"line-height: 100%; outline: none; text-decoration: none; vertical-align: baseline; font-size: 0; border: 0 none; max-height: 40px;\">\n                                                                    </a>\n                                                                </td>\n                                                                <td class=\"bb-text-right\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right;\">\n                                                                    2025-08-15 17:17:31\n                                                                </td>\n                                                            </tr>\n                                                        </tbody>\n                                                    </table>\n                                                </td>\n                                            </tr>\n                                        </tbody>\n                                    </table>\n\n\n<div class=\"bb-main-content\">\n    <table class=\"bb-box\" cellpadding=\"0\" cellspacing=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; background: #ffffff; border-radius: 4px; -webkit-box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05); box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05); border: 1px solid #dce0e5; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\n        <tbody>\n            <tr>\n                <td class=\"bb-content bb-pb-0\" align=\"center\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 40px 48px; padding-bottom: 0;\">\n                    <table class=\"bb-icon bb-icon-lg bb-bg-blue\" cellspacing=\"0\" cellpadding=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 0; border-radius: 50%; background: #edeef0; line-height: 100%; font-weight: 300; border-collapse: separate; text-align: center; width: 72px; height: 72px; font-size: 48px; background-color: #206bc4; color: #ffffff; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\n                        <tbody>\n                            <tr>\n                                <td valign=\"middle\" align=\"center\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;\">\n                                    <img src=\"https://martfury.gc/vendor/core/core/base/images/email-icons/shopping-cart.png\" class=\"bb-va-middle\" width=\"40\" height=\"40\" alt=\"Icon\" style=\"border: 0 none; line-height: 100%; outline: none; text-decoration: none; font-size: 0; vertical-align: middle; display: block; width: 40px; height: 40px;\">\n                                </td>\n                            </tr>\n                        </tbody>\n                    </table>\n                    <h1 class=\"bb-text-center bb-m-0 bb-mt-md\" style=\"font-weight: 600; color: #232b42; font-size: 28px; line-height: 130%; text-align: center; margin: 0; margin-top: 16px;\">Order successfully!</h1>\n                </td>\n            </tr>\n            <tr>\n                <td class=\"bb-content\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 40px 48px;\">\n                    <div>Dear Global Office,</div>\n                    <div>You got a new order on MartFury!</div>\n                </td>\n            </tr>\n            <tr>\n                <td class=\"bb-content bb-pt-0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 40px 48px; padding-top: 0;\">\n                    <table class=\"bb-row bb-mb-md\" cellpadding=\"0\" cellspacing=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; table-layout: fixed; margin-bottom: 16px; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\n                        <tbody>\n                            <tr>\n                                <td class=\"bb-bb-col\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;\">\n                                    <h4 class=\"bb-m-0\" style=\"font-weight: 600; color: #232b42; font-size: 16px; margin: 0;\">Customer Information</h4>\n                                    <div>Name: <strong style=\"font-weight: 600;\">Fae Koelpin</strong>\n</div>\n                                                                        <div>Phone: <strong style=\"font-weight: 600;\">+18598017656</strong>\n</div>\n                                                                                                                                                <div>Address: <strong style=\"font-weight: 600;\">2358 Jadon Stream, Bashirianfurt, Utah, PE</strong>\n</div>\n                                                                    </td>\n                            </tr>\n                        </tbody>\n                    </table>\n                </td>\n            </tr>\n            <tr>\n                <td class=\"bb-content bb-pt-0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 40px 48px; padding-top: 0;\">\n                    <h4 style=\"font-weight: 600; margin: 0 0 0.5em; color: #232b42; font-size: 16px;\">Here's what you ordered:</h4>\n                    <a class=\"button button-blue\" href=\"https://martfury.gc/orders/tracking?order_id=%2310000054&amp;email=vendor%40botble.com\" style=\"color: #206bc4; text-decoration: none;\">View order</a>\n    or <a href=\"https://martfury.gc\" style=\"color: #206bc4; text-decoration: none;\">Go to our shop</a>\n\n    <br>\n\n<table class=\"bb-table\" cellspacing=\"0\" cellpadding=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\n    <thead>\n        <tr>\n            <th colspan=\"2\" style=\"text-transform: uppercase; font-weight: 600; color: #667382; font-size: 12px; padding: 0 0 4px 0; padding-left: 0;\"></th>\n            <th style=\"text-transform: uppercase; font-weight: 600; color: #667382; font-size: 12px; padding: 0 0 4px 0;\">Quantity</th>\n            <th class=\"bb-text-right\" style=\"text-align: right; text-transform: uppercase; font-weight: 600; color: #667382; font-size: 12px; padding: 0 0 4px 0; padding-right: 0;\">Price</th>\n        </tr>\n    </thead>\n\n    <tbody>\n                <tr>\n            <td class=\"bb-pr-0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding-right: 0; padding: 4px 0; padding-left: 0;\">\n                <a href=\"\" style=\"color: #206bc4; text-decoration: none;\">\n                    <img src=\"https://martfury.gc/storage/products/10-150x150.jpg\" class=\" bb-rounded\" width=\"64\" height=\"64\" alt=\"\" style=\"line-height: 100%; outline: none; text-decoration: none; vertical-align: baseline; font-size: 0; border: 0 none; border-radius: 4px;\">\n                </a>\n            </td>\n            <td class=\"bb-pl-md bb-w-100p\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; width: 100%; padding-left: 16px !important; padding: 4px 0;\">\n                <strong style=\"font-weight: 600;\">Herschel Leather Duffle Bag In Brown Color</strong><br>\n                                    <span class=\"bb-text-muted\" style=\"color: #667382;\">(Color: Blue, Size: L)</span>\n                \n                            </td>\n            <td class=\"bb-text-center\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: center; padding: 4px 0;\">x 1</td>\n            <td class=\"bb-text-right\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right; padding: 4px 0; padding-right: 0;\">SAR1,294.00</td>\n        </tr>\n    \n                                    <tr>\n                    <td colspan=\"2\" class=\"bb-border-top bb-text-right\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right; border-top: 1px solid #dce0e5; padding: 4px 0; padding-left: 0;\">Subtotal</td>\n                    <td colspan=\"2\" class=\"bb-border-top bb-text-right\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right; border-top: 1px solid #dce0e5; padding: 4px 0; padding-right: 0;\">SAR1,294.00</td>\n                </tr>\n            \n            \n                            <tr>\n                    <td colspan=\"2\" class=\"bb-text-right\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right; padding: 4px 0; padding-left: 0;\">Tax</td>\n                    <td colspan=\"2\" class=\"bb-text-right\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; text-align: right; padding: 4px 0; padding-right: 0;\">SAR129.40</td>\n                </tr>\n            \n            \n                        <tr>\n                <td colspan=\"2\" class=\"bb-text-right bb-font-strong bb-h3 bb-m-0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; color: #232b42; font-size: 20px; line-height: 130%; text-align: right; font-weight: 600; margin: 0; padding: 4px 0; padding-left: 0;\">Total</td>\n                <td colspan=\"2\" class=\"bb-font-strong bb-h3 bb-m-0 bb-text-right\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; color: #232b42; font-size: 20px; line-height: 130%; text-align: right; font-weight: 600; margin: 0; padding: 4px 0; padding-right: 0;\">SAR1,423.40</td>\n            </tr>\n            </tbody>\n</table>\n\n\n                                    </td>\n            </tr>\n            <tr>\n                <td class=\"bb-content bb-border-top\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding: 40px 48px; border-top: 1px solid #dce0e5;\">\n                    <table class=\"bb-row bb-mb-md\" cellpadding=\"0\" cellspacing=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; table-layout: fixed; margin-bottom: 16px; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\n                        <tbody>\n                            <tr>\n                                <td class=\"bb-bb-col\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;\">\n                                    <h4 class=\"bb-m-0\" style=\"font-weight: 600; color: #232b42; font-size: 16px; margin: 0;\">Order number</h4>\n                                    <div>#10000054</div>\n                                </td>\n                                <td class=\"bb-col-spacer\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; vertical-align: top; width: 24px;\"></td>\n                                <td class=\"bb-col\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; vertical-align: top;\">\n                                    <h4 class=\"bb-mb-0\" style=\"font-weight: 600; margin: 0 0 0.5em; color: #232b42; font-size: 16px; margin-bottom: 0;\">Order date</h4>\n                                    <div>2025-08-15 17:14:38</div>\n                                </td>\n                            </tr>\n                        </tbody>\n                    </table>\n                    <table class=\"bb-row\" cellpadding=\"0\" cellspacing=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; table-layout: fixed; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\n                        <tbody>\n                            <tr>\n                                <td class=\"bb-col\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; vertical-align: top;\">\n                                                                        <h4 class=\"bb-m-0\" style=\"font-weight: 600; color: #232b42; font-size: 16px; margin: 0;\">Shipping Method</h4>\n                                    <div>\n                                        Free delivery\n                                    </div>\n                                                                    </td>\n\n                                <td class=\"bb-col-spacer\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; vertical-align: top; width: 24px;\"></td>\n                                <td class=\"bb-col\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; vertical-align: top;\">\n                                    <h4 class=\"bb-m-0\" style=\"font-weight: 600; color: #232b42; font-size: 16px; margin: 0;\">Payment Method</h4>\n                                    <div>\n                                        HyperPay\n                                    </div>\n                                </td>\n                            </tr>\n                        </tbody>\n                    </table>\n                </td>\n            </tr>\n        </tbody>\n    </table>\n</div>\n\n<table cellspacing=\"0\" cellpadding=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\n    <tbody>\n        <tr>\n            <td class=\"bb-py-xl\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding-top: 48px; padding-bottom: 48px;\">\n                <table class=\"bb-text-center bb-text-muted\" cellspacing=\"0\" cellpadding=\"0\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; border-collapse: collapse; width: 100%; color: #667382; text-align: center; -premailer-cellpadding: 0; -premailer-cellspacing: 0;\">\n                    <tbody>\n                    \n                    <tr>\n                        <td class=\"bb-px-lg\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding-right: 24px; padding-left: 24px;\">\n                            © 2025 MartFury. All Rights Reserved.\n                        </td>\n                    </tr>\n\n                                            <tr>\n                            <td class=\"bb-pt-md\" style=\"font-family: Inter, -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; padding-top: 16px;\">\n                                If you have any questions, feel free to message us at <a href=\"mailto:<EMAIL>\" style=\"color: #206bc4; text-decoration: none;\"><EMAIL></a>.\n                            </td>\n                        </tr>\n                                        </tbody>\n                </table>\n            </td>\n        </tr>\n    </tbody>\n</table>\n</td>\n</tr>\n</tbody>\n</table>\n</td>\n</tr>\n</tbody>\n</table>\n</center>\n</body>\n\n</html>"}]}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "302 Found", "full_url": "https://martfury.gc/payment/hyperpay/callback?checkout_id=0ED2D1996DCA33C195FC346468A43449.uat01-vm-...", "action_name": "payments.hyperpay.callback", "controller_action": "Botble\\HyperPay\\Http\\Controllers\\HyperPayController@callback", "uri": "GET payment/hyperpay/callback", "controller": "Botble\\HyperPay\\Http\\Controllers\\HyperPayController@callback<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fhyperpay%2Fsrc%2FHttp%2FControllers%2FHyperPayController.php&line=60\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "Botble\\HyperPay\\Http\\Controllers", "prefix": "/payment/hyperpay", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmartfury%2Fplatform%2Fplugins%2Fhyperpay%2Fsrc%2FHttp%2FControllers%2FHyperPayController.php&line=60\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">platform/plugins/hyperpay/src/Http/Controllers/HyperPayController.php:60-186</a>", "middleware": "web, core", "duration": "5.53s", "peak_memory": "72MB", "response": "Redirect to https://martfury.gc/checkout/542d9a1b1e7195f913e91bc042defb6e/success", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-910107251 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>checkout_id</span>\" => \"<span class=sf-dump-str title=\"46 characters\">0ED2D1996DCA33C195FC346468A43449.uat01-vm-tx04</span>\"\n  \"<span class=sf-dump-key>order_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"46 characters\">0ED2D1996DCA33C195FC346468A43449.uat01-vm-tx04</span>\"\n  \"<span class=sf-dump-key>resourcePath</span>\" => \"<span class=sf-dump-str title=\"68 characters\">/v1/checkouts/0ED2D1996DCA33C195FC346468A43449.uat01-vm-tx04/payment</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-910107251\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1139718060 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1139718060\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1159007595 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">martfury.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"110 characters\">https://martfury.gc/payment/hyperpay/checkout/0ED2D1996DCA33C195FC346468A43449.uat01-vm-tx04?payment_type=visa</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3215 characters\">cookie_for_consent=1; botble_footprints_cookie=eyJpdiI6Ilp2Q1lFM2s1MkVpWnI2blVTb2FtZWc9PSIsInZhbHVlIjoiU1N3dm1pVUNyMlJieWRscUU2Rkk3dzFSVmsxSFhpU3Bpc0lSQmRvOEl3M0ZwaW42RkFpdUoxV1NOWW52Q2lOWFh1WUVZUFVpWnBGdHBuc2xVWXV6TmJFRUd6VVljQ25vWWlDaVhBczVpNEpTU0NmbytXczNaTzdDdFNCN3U4anoiLCJtYWMiOiJjZDdlODUyY2JjNzc4MjNmNmEwMTc4YzA3ODFlM2MyNmQ0MzhjMGNkMTA2ZDY3MjYwYjQ0MmMzMjA1NDAzMjhjIiwidGFnIjoiIn0%3D; botble_footprints_cookie_data=eyJpdiI6ImNlT05BQnNiOExZUk94aE9NbHNkYWc9PSIsInZhbHVlIjoiTWVoMVdkQWFROThBdm9VWUV1cWd6TFlLUFR2emErYW5PWWNDUk5ycVBWOURCdzVHT0pRaEpET242bzEzSFh0bWlpT0RZZnoxVUd4U0swOGUyV1lteXU0SDJhdG80U0VhdnVHaFVFVUJ0amhRVFhTOEFMWUpnYmVmamdqQ2NrNERDMW1lbElBeWNYN2NHQTAyZm9PTk5FKzIvT3VjRjU3ZU40bjJVNGI5WmdSTHBoZTZrUnp0RE9MbEg0aytxMUVlNTlJTW9lZnJGVWxDbHI3Q3poR2RDbzVISnB1Y0pCSXRDb0cyYmFCRUVRMkl2RkxUQlpoanhhR2pINW1ZdkZmS091REtuSnRlNjB6ekR2clEweFZlMjh1M0dOanZQdTlpcHdTRlg2YzJxQm92VHRPdG9SekNPSEJVa0VKcjlEZjlJenVONVVwZTJnRjQ4N1JZKzhEd2pnZHJsUkM3eXVaMmhYblhRZmVoQlk0YTNSOFFYUEhqYi9hQzZIWkloa0llWGNaMEFYVFFmVGhnYnZJcnJIUHh0Z2pzREpUb2ptM1BnU25wV3M3eWF2RUl5UVdwU0UzUDdCZkErMXlCREdRSXExTzVVK1kwL0Jmd0laTGZ1VE9sc1BMbG5lVnVLNytLaEs1YjJpTEllK3Qwd3VUbHBEL3JLNlo0VEMwN2UxakRKK2hxUE9QTmI5TDdqeW03WkgwZjVnPT0iLCJtYWMiOiI1ZmIxN2YwMTBlNWZjMTY0MTY0NDMzZDUyZmZjNmQ5ZWZkYjk0NTYxZjg0ZTk4ODAzNTVlYzJhYWJmN2I0NmVhIiwidGFnIjoiIn0%3D; shortcode_cache_suggestion_dismissed=1; widget_cache_suggestion_dismissed=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Imtmd01aTUlkUmlvaEJqSmRGMnJ5aXc9PSIsInZhbHVlIjoiMGVSOUY2VWtsSitnWmpMdXIxbHd1WC9PeGRISXNyNXQwM1dJeXBwUVZweUphTlViRWNWT09zVXpYUVpNWm1nTEJXeDlxQmlZTDUrOUNZdk9POERqSzQ0V0N4NXhGMFlyd1hPa2xKVGZjTjhWSWFQbWpRcmdGT1ViRFEyQ2IrMjUwMXNtOW1hbWM0MU5xR2UwdmkycTUyTlNibmRhdENJeXZnYkVlQlFlSkZYVm9BZk43WXU0RUhtOWt5NFZNVTJlN3BSMDRkaUd2Z0RwNzBqei9RUDZRVVhpWm5idEpKK3dJTWJ2dVg1OVQ0Zz0iLCJtYWMiOiJlZTQ5Mzc4NzBkNjk4ODUxYjJmNzI5ZDljM2E1OGY4NzM4YzUyYzc1MGFhYzgwMGY2NjJmM2JlN2Q5OWUwODJmIiwidGFnIjoiIn0%3D; botble_cookie_newsletter=1; remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpibjFnQzQrTDFHeWZNOTFoK1F4R2c9PSIsInZhbHVlIjoiWlBCZ2pyclRiZU9razRVZjF3YU1JVjl3ZlcrTVZKVW9JVlplS2lQOStvUVdjM0RCeWx3a2YrOEpicWtScGdBcXJtY1hZZ3BJOTdhR2dHdmRPYXB0VlZjcG5Mb1hjYWc3TnNxT1Z3RVJZaEM0VXRsSjMvWHp1VTIzR3gvTmJtbUh1dlhiSFhjZzZqMU1JVUVjRVQwZURXeUt5eGFuclh4TVFlSkdyeFQzMVkvdGhRWVlsenZPSG8rNWRNUVU5RG9KYUh6S1YwWmVwOUtCTWd5ckROdnpkZ3gxeTJ1WWdpR29mMUJ6TUpRUTdHQT0iLCJtYWMiOiI3M2Q5NDM1Y2Q2ZWU1NTMzNzczMjdhYWM3ZmRjNTJhZTFjNThiNTFlOTllZGZhM2RlOTIwNWI0OWI1M2U2MGI0IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjlTTEUyRStTUW5tcWp6azMwNldIc1E9PSIsInZhbHVlIjoidC8rcGZUZEVlWUgwS3U3eXFwMUpRbzZoVC83bUtVc1pHNXZ1ZDJZcGJ4RmlvekdlWGkwTklVREJvTGwrTmpab1V1RjRnY3RGbEtUbC92V3RtWEVFa2tyeHJSaWVaYVI0Ukg1bC9pc29weEtSMTNtTUU5NGJZU3FGYXE3MjJPR20iLCJtYWMiOiIxMzg5YWY0MDcyOTQxMGI0OTUyMzRmNmIxNmNkZmNjOTgwZmRlMjc5Yjc2MmUzYTU2NjI3ZGJiMGI0OTYyNmUzIiwidGFnIjoiIn0%3D; botble_session=eyJpdiI6IncrQ2lHOVh5d1JMWmFWMEVpVVJmUmc9PSIsInZhbHVlIjoiK2JDbVpJanhEekdCNEZHOUZaODQ5UXY4K29wcFhEbk9GUUVsMmFvRllyNGtoREgvRkJLRTJXb1dwNUxOK2hLL3U4clFEck5BMzJvM2dyTmVETCtlRmpFbzRtTHVkc2JRUytKZVpFSmxsOEZvTDU5OS8zdVh5TWFkZXB0TldVZy8iLCJtYWMiOiI2YWVjY2JkNzdhZTgwY2YzNjdmZDMyNGRkOTAyOTEzZmUzZmIxNzlmMzUyZjZmMzBjOWYwZDFhMTIzMWYwZTU0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1159007595\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1969968444 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>botble_footprints_cookie</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5d4a7afb407de91a3f785dca41f29624d4035861</span>\"\n  \"<span class=sf-dump-key>botble_footprints_cookie_data</span>\" => \"<span class=sf-dump-str title=\"347 characters\">{&quot;footprint&quot;:&quot;5d4a7afb407de91a3f785dca41f29624d4035861&quot;,&quot;ip&quot;:&quot;127.0.0.1&quot;,&quot;landing_domain&quot;:&quot;martfury.gc&quot;,&quot;landing_page&quot;:&quot;\\/&quot;,&quot;landing_params&quot;:null,&quot;referral&quot;:null,&quot;gclid&quot;:null,&quot;fclid&quot;:null,&quot;utm_source&quot;:null,&quot;utm_campaign&quot;:null,&quot;utm_medium&quot;:null,&quot;utm_term&quot;:null,&quot;utm_content&quot;:null,&quot;referrer_url&quot;:&quot;http:\\/\\/localhost\\/&quot;,&quot;referrer_domain&quot;:&quot;localhost&quot;}</span>\"\n  \"<span class=sf-dump-key>shortcode_cache_suggestion_dismissed</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>widget_cache_suggestion_dismissed</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|mr2hzXolFPMrElNypPbMsV1VAiAKgY80Q2lBNyezSZXf4Bow8DqaNYnj6c6c|$2y$12$faChkoM7UV9t8mCoo6oJFOLYCOlwKR0CpAW2E1NPY0Bj1MIeVQw92</span>\"\n  \"<span class=sf-dump-key>botble_cookie_newsletter</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">2|rEY2fnBoxjVikI7FHOoHpfsUL6HmShbqbiBbMajXCGvqp4DJneBXZNsMDEhJ|$2y$12$mAIYinlq8PEmipS0gyL5O.c3YY4axUf.8ExV94mmLdgzhultxp2KS</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KzcOpLY5BY4Qmb2ysxF4UMkeFxNzIwhg5XNVcWQ7</span>\"\n  \"<span class=sf-dump-key>botble_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">rOB2sRYY2tEGNk5tY11YcouH4tG83UStulFD0FGr</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1969968444\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-351868576 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 15 Aug 2025 17:17:31 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"69 characters\">https://martfury.gc/checkout/542d9a1b1e7195f913e91bc042defb6e/success</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-351868576\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-80304885 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">KzcOpLY5BY4Qmb2ysxF4UMkeFxNzIwhg5XNVcWQ7</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>math-captcha</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>first</span>\" => <span class=sf-dump-num>4</span>\n    \"<span class=sf-dump-key>second</span>\" => <span class=sf-dump-num>8</span>\n    \"<span class=sf-dump-key>operand</span>\" => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"110 characters\">https://martfury.gc/payment/hyperpay/checkout/0ED2D1996DCA33C195FC346468A43449.uat01-vm-tx04?payment_type=visa</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">success_msg</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>cart</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>cart_updated_at</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @1755278222\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @1755278222</span></span> {<a class=sf-dump-ref href=#sf-dump-80304885-ref24668 title=\"3 occurrences\">#4668</a><samp data-depth=3 id=sf-dump-80304885-ref24668 class=sf-dump-compact>\n      #<span class=sf-dump-protected title=\"Protected property\">endOfTime</span>: <span class=sf-dump-const>false</span>\n      #<span class=sf-dump-protected title=\"Protected property\">startOfTime</span>: <span class=sf-dump-const>false</span>\n      #<span class=sf-dump-protected title=\"Protected property\">constructedObjectId</span>: \"<span class=sf-dump-str title=\"32 characters\">000000000000123c0000000000000000</span>\"\n      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Carbon\\Carbon`\">clock</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localMonthsOverflow</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localYearsOverflow</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localStrictModeEnabled</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localHumanDiffOptions</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localToStringFormat</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localSerializer</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localMacros</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localGenericMacros</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localFormatFunction</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">localTranslator</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">dumpProperties</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">date</span>\"\n        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">timezone_type</span>\"\n        <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">timezone</span>\"\n      </samp>]\n      #<span class=sf-dump-protected title=\"Protected property\">dumpLocale</span>: <span class=sf-dump-const>null</span>\n      #<span class=sf-dump-protected title=\"Protected property\">dumpDateProperties</span>: <span class=sf-dump-const>null</span>\n      <span class=sf-dump-meta>date</span>: <span class=sf-dump-const title=\"Friday, August 15, 2025\n- 00:00:29.46207 from now\nDST Off\">2025-08-15 17:17:02.125610 UTC (+00:00)</span>\n    </samp>}\n  </samp>]\n  \"<span class=sf-dump-key>selected_payment_method</span>\" => \"<span class=sf-dump-str title=\"8 characters\">hyperpay</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>tracked_start_checkout</span>\" => \"<span class=sf-dump-str title=\"32 characters\">542d9a1b1e7195f913e91bc042defb6e</span>\"\n  \"<span class=sf-dump-key>b96fb90920ecb89bf482f629b96cf99b</span>\" => <span class=sf-dump-note>array:14</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>promotion_discount_amount</span>\" => <span class=sf-dump-num>0</span>\n    \"<span class=sf-dump-key>address_id</span>\" => <span class=sf-dump-num>3</span>\n    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Fae Koelpin</span>\"\n    \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"12 characters\">+13198941196</span>\"\n    \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n    \"<span class=sf-dump-key>country</span>\" => \"<span class=sf-dump-str title=\"2 characters\">PE</span>\"\n    \"<span class=sf-dump-key>state</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Utah</span>\"\n    \"<span class=sf-dump-key>city</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Bashirianfurt</span>\"\n    \"<span class=sf-dump-key>address</span>\" => \"<span class=sf-dump-str title=\"17 characters\">2358 Jadon Stream</span>\"\n    \"<span class=sf-dump-key>zip_code</span>\" => \"<span class=sf-dump-str title=\"10 characters\">09306-7143</span>\"\n    \"<span class=sf-dump-key>marketplace</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-key>2</span> => <span class=sf-dump-note>array:28</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>address_id</span>\" => <span class=sf-dump-num>3</span>\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Fae Koelpin</span>\"\n        \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"12 characters\">+13198941196</span>\"\n        \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n        \"<span class=sf-dump-key>country</span>\" => \"<span class=sf-dump-str title=\"2 characters\">PE</span>\"\n        \"<span class=sf-dump-key>state</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Utah</span>\"\n        \"<span class=sf-dump-key>city</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Bashirianfurt</span>\"\n        \"<span class=sf-dump-key>address</span>\" => \"<span class=sf-dump-str title=\"17 characters\">2358 Jadon Stream</span>\"\n        \"<span class=sf-dump-key>zip_code</span>\" => \"<span class=sf-dump-str title=\"10 characters\">09306-7143</span>\"\n        \"<span class=sf-dump-key>created_order</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @1755278222\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @1755278222</span></span> {<a class=sf-dump-ref href=#sf-dump-80304885-ref24668 title=\"3 occurrences\">#4668</a>}\n        \"<span class=sf-dump-key>created_order_id</span>\" => <span class=sf-dump-num>54</span>\n        \"<span class=sf-dump-key>is_save_order_shipping_address</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>created_order_address</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>created_order_address_id</span>\" => <span class=sf-dump-num>48</span>\n        \"<span class=sf-dump-key>created_order_product</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Carbon\\Carbon @1755278222\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Carbon</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Carbon @1755278222</span></span> {<a class=sf-dump-ref href=#sf-dump-80304885-ref24668 title=\"3 occurrences\">#4668</a>}\n        \"<span class=sf-dump-key>coupon_discount_amount</span>\" => <span class=sf-dump-num>0</span>\n        \"<span class=sf-dump-key>applied_coupon_code</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>is_free_shipping</span>\" => <span class=sf-dump-const>false</span>\n        \"<span class=sf-dump-key>promotion_discount_amount</span>\" => <span class=sf-dump-num>0</span>\n        \"<span class=sf-dump-key>shipping_method</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n        \"<span class=sf-dump-key>shipping_option</span>\" => \"<span class=sf-dump-str>1</span>\"\n        \"<span class=sf-dump-key>shipping_amount</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"\n        \"<span class=sf-dump-key>shipping</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>default</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-key>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Free delivery</span>\"\n              \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"\n            </samp>]\n            <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Local Pickup</span>\"\n              \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"\n            </samp>]\n            <span class=sf-dump-key>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Flat Rate</span>\"\n              \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">20.00</span>\"\n            </samp>]\n          </samp>]\n        </samp>]\n        \"<span class=sf-dump-key>default_shipping_method</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n        \"<span class=sf-dump-key>default_shipping_option</span>\" => \"<span class=sf-dump-str>1</span>\"\n        \"<span class=sf-dump-key>is_available_shipping</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>billing_address_same_as_shipping_address</span>\" => <span class=sf-dump-const>true</span>\n        \"<span class=sf-dump-key>billing_address</span>\" => []\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>billing_address_same_as_shipping_address</span>\" => <span class=sf-dump-const>true</span>\n    \"<span class=sf-dump-key>billing_address</span>\" => []\n    \"<span class=sf-dump-key>is_new_address</span>\" => <span class=sf-dump-const>false</span>\n  </samp>]\n  \"<span class=sf-dump-key>order_id</span>\" => <span class=sf-dump-num>54</span>\n  \"<span class=sf-dump-key>success_msg</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Payment completed successfully!</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-80304885\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "https://martfury.gc/payment/hyperpay/callback?checkout_id=0ED2D1996DCA33C195FC346468A43449.uat01-vm-...", "action_name": "payments.hyperpay.callback", "controller_action": "Botble\\HyperPay\\Http\\Controllers\\HyperPayController@callback"}, "badge": "302 Found"}}