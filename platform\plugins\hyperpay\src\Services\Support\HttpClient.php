<?php

namespace Botble\HyperPay\Services\Support;

use G<PERSON><PERSON><PERSON>ttp\Client as GuzzleClient;
use Guz<PERSON>Http\Exception\RequestException;
use GuzzleHttp\Psr7\Response;
use Illuminate\Support\Facades\Log;

final class HttpClient
{
    /**
     * @var array
     */
    protected $config = [];

    /**
     * @var GuzzleClient
     */
    protected $client;

    /**
     * @var string
     */
    protected $path;

    /**
     * Create a new HTTP client instance.
     *
     * @param  \GuzzleHttp\Client  $client
     * @param  string  $path
     * @param  array  $config
     * @return void
     */
    public function __construct(GuzzleClient $client, string $path, array $config)
    {
        $this->client = $client;
        $this->config = $config;
        $this->path = $path;
    }

    /**
     * Create a POST server-to-server request.
     *
     * @param  array  $parameters
     * @return Response
     */
    public function post(array $parameters): Response
    {
        try {
            Log::info('HyperPay HTTP Client POST Request', [
                'url' => $this->path,
                'parameters' => $parameters,
                'access_token_present' => !empty($this->config['access_token'])
            ]);

            $response = $this->client->post($this->path, [
                'form_params' => $parameters,
                'headers' => [
                    'Content-Type' => 'application/x-www-form-urlencoded; charset=UTF-8',
                    'Authorization' => 'Bearer ' . $this->config['access_token'],
                ],
            ]);

            Log::info('HyperPay HTTP Client POST Response', [
                'status_code' => $response->getStatusCode(),
                'response_body' => $response->getBody()->getContents()
            ]);

            // Reset body position for further reading
            $response->getBody()->rewind();

            return $response;
        } catch (RequestException $e) {
            $response = $e->getResponse();
            
            Log::error('HyperPay HTTP Client POST Error', [
                'error' => $e->getMessage(),
                'status_code' => $response ? $response->getStatusCode() : null,
                'response_body' => $response ? $response->getBody()->getContents() : null,
                'url' => $this->path,
                'parameters' => $parameters
            ]);

            if ($response) {
                $response->getBody()->rewind();
            }

            return $response;
        }
    }

    /**
     * Create a GET request to HyperPay used to check the status.
     *
     * @param  array  $parameters
     * @return Response
     */
    public function get(array $parameters): Response
    {
        try {
            Log::info('HyperPay HTTP Client GET Request', [
                'url' => $this->path,
                'parameters' => $parameters,
                'access_token_present' => !empty($this->config['access_token'])
            ]);

            $response = $this->client->get($this->path, [
                'query' => $parameters,
                'headers' => [
                    'Authorization' => 'Bearer ' . $this->config['access_token'],
                    'Content-Type' => 'application/x-www-form-urlencoded; charset=UTF-8',
                ],
            ]);

            Log::info('HyperPay HTTP Client GET Response', [
                'status_code' => $response->getStatusCode(),
                'response_body' => $response->getBody()->getContents()
            ]);

            // Reset body position for further reading
            $response->getBody()->rewind();

            return $response;
        } catch (RequestException $e) {
            $response = $e->getResponse();
            
            Log::error('HyperPay HTTP Client GET Error', [
                'error' => $e->getMessage(),
                'status_code' => $response ? $response->getStatusCode() : null,
                'response_body' => $response ? $response->getBody()->getContents() : null,
                'url' => $this->path,
                'parameters' => $parameters
            ]);

            if ($response) {
                $response->getBody()->rewind();
            }

            return $response;
        }
    }
}
