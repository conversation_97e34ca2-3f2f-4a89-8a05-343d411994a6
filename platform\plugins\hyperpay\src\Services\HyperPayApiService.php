<?php

namespace Bo<PERSON>ble\HyperPay\Services;

use Bo<PERSON>ble\HyperPay\Contracts\BillingInterface;
use <PERSON><PERSON>ble\HyperPay\Services\Support\HttpClient;
use Bo<PERSON>ble\HyperPay\Services\Support\HttpParameters;
use Botble\HyperPay\Services\Support\HttpResponse;
use GuzzleHttp\Client as GuzzleClient;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class HyperPayApiService
{
    /**
     * @var GuzzleClient
     */
    protected $client;

    /**
     * @var array
     */
    protected $config = [];

    /**
     * @var BillingInterface|null
     */
    protected $billing;

    /**
     * @var string
     */
    protected $token;

    /**
     * @var string
     */
    protected $brand;

    /**
     * @var string
     */
    protected $redirectUrl;

    /**
     * @var string
     */
    protected $gatewayUrl = 'https://eu-test.oppwa.com';

    /**
     * @var bool
     */
    protected $registerUserCard = false;

    /**
     * Create a new HyperPay API service instance.
     *
     * @param  GuzzleClient|null  $client
     */
    public function __construct(?GuzzleClient $client = null)
    {
        $this->client = $client ?: new GuzzleClient();
        $this->loadConfig();
    }

    /**
     * Load configuration from settings.
     */
    protected function loadConfig(): void
    {
        $this->config = [
            'access_token' => get_payment_setting('access_token', HYPERPAY_PAYMENT_METHOD_NAME),
            'entityId' => get_payment_setting('visa_entity_id', HYPERPAY_PAYMENT_METHOD_NAME),
            'currency' => get_payment_setting('currency', HYPERPAY_PAYMENT_METHOD_NAME, 'SAR'),
            'notificationUrl' => '/hyperpay/webhook',
        ];

        $isSandbox = get_payment_setting('sandbox_mode', HYPERPAY_PAYMENT_METHOD_NAME, false);
        if (!$isSandbox) {
            $this->gatewayUrl = 'https://eu-prod.oppwa.com';
        }

        Log::info('HyperPay API Service Config Loaded', [
            'gateway_url' => $this->gatewayUrl,
            'currency' => $this->config['currency'],
            'has_access_token' => !empty($this->config['access_token']),
            'has_entity_id' => !empty($this->config['entityId']),
            'sandbox_mode' => $isSandbox
        ]);
    }

    /**
     * Set the MADA entity ID in the parameters.
     */
    public function mada(): self
    {
        $this->config['entityId'] = get_payment_setting('mada_entity_id', HYPERPAY_PAYMENT_METHOD_NAME);
        return $this;
    }

    /**
     * Set the Apple Pay entity ID in the parameters.
     */
    public function setApplePayEntityId(): self
    {
        $this->config['entityId'] = get_payment_setting('applepay_entity_id', HYPERPAY_PAYMENT_METHOD_NAME);
        return $this;
    }

    /**
     * Set the AMEX entity ID in the parameters.
     */
    public function setAmexEntityId(): self
    {
        $this->config['entityId'] = get_payment_setting('amex_entity_id', HYPERPAY_PAYMENT_METHOD_NAME);
        return $this;
    }

    /**
     * Add billing data to the payment body.
     *
     * @param  BillingInterface  $billing
     * @return $this
     */
    public function addBilling(BillingInterface $billing): self
    {
        $this->billing = $billing;
        return $this;
    }

    /**
     * Prepare the checkout.
     *
     * @param  array  $trackableData
     * @param  Model|null  $user
     * @param  float  $amount
     * @param  string  $brand
     * @param  Request  $request
     * @return array
     */
    public function checkout(array $trackableData, ?Model $user, $amount, $brand, Request $request): array
    {
        $this->brand = $brand;

        // Set appropriate entity ID based on brand
        if (strtolower($this->brand) === 'mada') {
            $this->mada();
        } elseif (strtolower($this->brand) === 'applepay') {
            $this->setApplePayEntityId();
        } elseif (strtolower($this->brand) === 'amex') {
            $this->setAmexEntityId();
        }

        $trackableData = array_merge($trackableData, [
            'amount' => $amount,
        ]);

        return $this->prepareCheckout($user, $trackableData, $request);
    }

    /**
     * Define the data used to generate a successful response from HyperPay.
     *
     * @param  Model|null  $user
     * @param  array  $trackableData
     * @param  Request  $request
     * @return array
     */
    protected function prepareCheckout(?Model $user, array $trackableData, Request $request): array
    {
        $this->token = $this->generateToken();
        $this->config['merchantTransactionId'] = $this->token;
        $this->config['userAgent'] = $request->server('HTTP_USER_AGENT');

        try {
            $httpClient = new HttpClient(
                $this->client,
                $this->gatewayUrl . '/v1/checkouts',
                $this->config
            );

            $httpParameters = new HttpParameters();

            // Extract customer data as array (like other plugins)
            $customerData = [
                'email' => $trackableData['customer_email'] ?? null,
                'name' => $trackableData['customer_name'] ?? null,
            ];

            $parameters = $httpParameters->postParams(
                Arr::get($trackableData, 'amount'),
                $customerData,
                $this->config,
                $this->billing,
                $this->registerUserCard
            );

            $result = $httpClient->post($parameters);

            $response = (new HttpResponse($result, null, $parameters))
                ->setUser($user)
                ->setTrackableData($trackableData)
                ->addScriptUrl($this->gatewayUrl)
                ->addShopperResultUrl($this->redirectUrl)
                ->prepareCheckout();

            return $response;
        } catch (\Exception $e) {
            Log::error('HyperPay API Service Checkout Error', [
                'error' => $e->getMessage(),
                'trackable_data' => $trackableData,
                'config' => array_merge($this->config, ['access_token' => '***hidden***'])
            ]);

            return [
                'error' => true,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Check the payment status using resourcePath and checkout_id.
     *
     * @param  string  $resourcePath
     * @param  string  $checkoutId
     * @return array
     */
    public function paymentStatus(string $resourcePath, string $checkoutId): array
    {
        try {
            $httpClient = new HttpClient(
                $this->client,
                $this->gatewayUrl . $resourcePath,
                $this->config
            );

            $httpParameters = new HttpParameters();
            $parameters = $httpParameters->getParams($checkoutId);

            $result = $httpClient->get($parameters);

            $response = new HttpResponse($result);
            return $response->paymentStatus();
        } catch (\Exception $e) {
            Log::error('HyperPay API Service Payment Status Error', [
                'error' => $e->getMessage(),
                'resource_path' => $resourcePath,
                'checkout_id' => $checkoutId
            ]);

            return [
                'error' => true,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Process recurring payment.
     *
     * @param  string  $registrationId
     * @param  float  $amount
     * @param  string  $checkoutId
     * @return array
     */
    public function recurringPayment(string $registrationId, $amount, $checkoutId): array
    {
        try {
            $httpClient = new HttpClient(
                $this->client,
                $this->gatewayUrl . '/v1/registrations/' . $registrationId . '/payments',
                $this->config
            );

            $httpParameters = new HttpParameters();
            $parameters = $httpParameters->postRecurringPayment($amount, $this->redirectUrl, $checkoutId);

            $result = $httpClient->post($parameters);

            $response = new HttpResponse($result, null, []);
            return $response->recurringPayment();
        } catch (\Exception $e) {
            Log::error('HyperPay API Service Recurring Payment Error', [
                'error' => $e->getMessage(),
                'registration_id' => $registrationId,
                'amount' => $amount,
                'checkout_id' => $checkoutId
            ]);

            return [
                'error' => true,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Add merchant transaction ID.
     *
     * @param  string  $id
     * @return $this
     */
    public function addMerchantTransactionId($id): self
    {
        $this->token = $id;
        return $this;
    }

    /**
     * Add redirection URL to the shopper to finalize the payment.
     *
     * @param  string  $url
     * @return $this
     */
    public function addRedirectUrl($url): self
    {
        $this->redirectUrl = $url;
        return $this;
    }

    /**
     * Set the register user card information.
     *
     * @return $this
     */
    public function registerUserCard(): self
    {
        $this->registerUserCard = true;
        return $this;
    }

    /**
     * Generate the token used as merchantTransactionId.
     *
     * @return string
     */
    private function generateToken(): string
    {
        return $this->token ?: Str::random(64);
    }

    /**
     * Get the gateway URL.
     *
     * @return string
     */
    public function getGatewayUrl(): string
    {
        return $this->gatewayUrl;
    }

    /**
     * Get the current configuration.
     *
     * @return array
     */
    public function getConfig(): array
    {
        return $this->config;
    }
}
