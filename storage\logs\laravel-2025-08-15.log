[2025-08-15 16:53:07] local.INFO: HyperPay API Service Config Loaded {"gateway_url":"https://eu-test.oppwa.com","currency":"SAR","has_access_token":true,"has_entity_id":true,"sandbox_mode":"1"} 
[2025-08-15 16:53:13] local.INFO: HyperPay API Service Config Loaded {"gateway_url":"https://eu-test.oppwa.com","currency":"SAR","has_access_token":true,"has_entity_id":true,"sandbox_mode":"1"} 
[2025-08-15 16:53:13] local.INFO: HyperPay API Service Config Loaded {"gateway_url":"https://eu-test.oppwa.com","currency":"SAR","has_access_token":true,"has_entity_id":true,"sandbox_mode":"1"} 
[2025-08-15 16:53:16] local.INFO: HyperPay API Service Config Loaded {"gateway_url":"https://eu-test.oppwa.com","currency":"SAR","has_access_token":true,"has_entity_id":true,"sandbox_mode":"1"} 
[2025-08-15 16:53:20] local.INFO: HyperPay API Service Config Loaded {"gateway_url":"https://eu-test.oppwa.com","currency":"SAR","has_access_token":true,"has_entity_id":true,"sandbox_mode":"1"} 
[2025-08-15 16:53:20] local.INFO: HyperPay Hook Service Provider - Payment Data {"payment_data":{"amount":528.0,"payment_fee":0.0,"shipping_amount":0.0,"shipping_method":"Default","tax_amount":48.0,"discount_amount":0.0,"currency":"SAR","order_id":[53],"description":"Pay for your order #53 at martfury.gc","customer_id":2,"customer_type":"Botble\\Ecommerce\\Models\\Customer","return_url":"https://martfury.gc/checkout/2bbdbd08f9ec9fc36d8b3e7ced0cf369/success","cancel_url":"https://martfury.gc/checkout/2bbdbd08f9ec9fc36d8b3e7ced0cf369?error=1&error_type=payment","callback_url":"https://martfury.gc/checkout/2bbdbd08f9ec9fc36d8b3e7ced0cf369/success","products":[{"id":38,"name":"Nikon HD camera","image":"https://martfury.gc/storage/products/6.jpg","price":480.0,"price_per_order":528.0,"qty":1}],"orders":{"Illuminate\\Database\\Eloquent\\Collection":[{"id":53,"code":"#10000052","user_id":2,"shipping_option":"3","shipping_method":{"value":"default","label":"Default"},"status":{"value":"pending","label":"Pending"},"amount":"528.00","tax_amount":"48.00","shipping_amount":"0.00","payment_fee":"0.00","description":null,"coupon_code":null,"discount_amount":"0.00","sub_total":"480.00","is_confirmed":0,"discount_description":null,"is_finished":1,"cancellation_reason":null,"cancellation_reason_description":null,"completed_at":null,"token":"2bbdbd08f9ec9fc36d8b3e7ced0cf369","payment_id":93,"created_at":"2025-08-15T16:37:22.000000Z","updated_at":"2025-08-15T16:53:20.000000Z","proof_file":null,"store_id":3,"private_notes":null,"address":{"id":47,"name":"Fae Koelpin","phone":"+13198941196","email":"<EMAIL>","country":"PE","state":"Utah","city":"Bashirianfurt","address":"2358 Jadon Stream","order_id":53,"zip_code":"09306-7143","type":{"value":"shipping_address","label":"Shipping address"}},"products":[{"id":56,"order_id":53,"qty":1,"price":"480.00","tax_amount":"48.00","options":{"image":"products/6.jpg","attributes":"(Color: Black, Size: XXL)","taxRate":10,"taxClasses":{"VAT":10},"options":[],"extras":[],"sku":"SW-125-A0","weight":886},"product_options":[],"product_id":38,"product_name":"Nikon HD camera","product_image":"products/6.jpg","weight":886.0,"restock_quantity":0,"created_at":"2025-08-15T16:37:22.000000Z","updated_at":"2025-08-15T16:53:20.000000Z","product_type":"physical","times_downloaded":0,"license_code":null,"downloaded_at":null,"product":{"id":38,"name":"Nikon HD camera","description":null,"content":null,"status":{"value":"published","label":"Published"},"images":["products/6.jpg"],"video_media":null,"sku":"SW-125-A0","order":0,"quantity":17,"allow_checkout_when_out_of_stock":false,"with_storehouse_management":true,"is_featured":false,"brand_id":3,"is_variation":true,"sale_type":0,"price":480.0,"sale_price":null,"start_date":null,"end_date":null,"length":12.0,"wide":11.0,"height":17.0,"weight":886.0,"tax_id":null,"views":0,"created_at":"2024-10-13T22:15:53.000000Z","updated_at":"2025-08-15T16:38:06.000000Z","stock_status":{"value":"in_stock","label":"In stock"},"created_by_id":0,"created_by_type":"Botble\\ACL\\Models\\User","image":"products/6.jpg","product_type":{"value":"physical","label":"Physical"},"barcode":null,"cost_per_item":null,"generate_license_code":false,"license_code_type":"auto_generate","minimum_order_quantity":0,"maximum_order_quantity":0,"notify_attachment_updated":false,"specification_table_id":null,"store_id":null,"approved_by":0,"original_price":480.0,"front_sale_price":480.0,"variation_info":{"id":15,"product_id":38,"configurable_product_id":6,"is_default":1,"configurable_product":{"id":6,"name":"Nikon HD camera","description":"<ul><li> Unrestrained and portable active stereo speaker</li>

            <li> Free from the confines of wires and chords</li>

            <li> 20 hours of portable capabilities</li>

            <li> Double-ended Coil Cord with 3.5mm Stereo Plugs Included</li>

            <li> 3/4″ Dome Tweeters: 2X and 4″ Woofer: 1X</li></ul>","content":"<p>Short Hooded Coat features a straight body, large pockets with button flaps, ventilation air holes, and a string detail along the hemline. The style is completed with a drawstring hood, featuring Rains’ signature built-in cap. Made from waterproof, matte PU, this lightweight unisex rain jacket is an ode to nostalgia through its classic silhouette and utilitarian design details.</p>

                                <p>- Casual unisex fit</p>



                                <p>- 64% polyester, 36% polyurethane</p>



                                <p>- Water column pressure: 4000 mm</p>



                                <p>- Model is 187cm tall and wearing a size S / M</p>



                                <p>- Unisex fit</p>



                                <p>- Drawstring hood with built-in cap</p>



                                <p>- Front placket with snap buttons</p>



                                <p>- Ventilation under armpit</p>



                                <p>- Adjustable cuffs</p>



                                <p>- Double welted front pockets</p>



                                <p>- Adjustable elastic string at hempen</p>



                                <p>- Ultrasonically welded seams</p>



                                <p>This is a unisex item, please check our clothing & footwear sizing guide for specific Rains jacket sizing information. RAINS comes from the rainy nation of Denmark at the edge of the European continent, close to the ocean and with prevailing westerly winds; all factors that contribute to an average of 121 rain days each year. Arising from these rainy weather conditions comes the attitude that a quick rain shower may be beautiful, as well as moody- but first and foremost requires the right outfit. Rains focus on the whole experience of going outside on rainy days, issuing an invitation to explore even in the most mercurial weather.</p>","status":{"value":"published","label":"Published"},"images":["products/6.jpg"],"video_media":null,"sku":"SW-125-A0","order":0,"quantity":17,"allow_checkout_when_out_of_stock":false,"with_storehouse_management":true,"is_featured":true,"brand_id":3,"is_variation":false,"sale_type":0,"price":480.0,"sale_price":null,"start_date":null,"end_date":null,"length":12.0,"wide":11.0,"height":17.0,"weight":886.0,"tax_id":null,"views":157528,"created_at":"2024-10-13T22:15:52.000000Z","updated_at":"2025-08-15T16:38:06.000000Z","stock_status":{"value":"in_stock","label":"In stock"},"created_by_id":0,"created_by_type":"Botble\\ACL\\Models\\User","image":"products/6.jpg","product_type":{"value":"physical","label":"Physical"},"barcode":null,"cost_per_item":null,"generate_license_code":false,"license_code_type":"auto_generate","minimum_order_quantity":0,"maximum_order_quantity":0,"notify_attachment_updated":false,"specification_table_id":2,"store_id":3,"approved_by":0,"original_price":480.0,"front_sale_price":480.0}}}}]}]},"address":{"name":"Fae Koelpin","email":"<EMAIL>","phone":"+13198941196","country":"PE","state":"Utah","city":"Bashirianfurt","address":"2358 Jadon Stream","zip_code":"09306-7143"},"checkout_token":"2bbdbd08f9ec9fc36d8b3e7ced0cf369"},"order_id_type":"array","order_id_value":[53]} 
[2025-08-15 16:53:20] local.INFO: HyperPay makePayment called with data: {"amount":528.0,"currency":"SAR","order_id":[53],"payment_type":"visa","data_keys":["amount","payment_fee","shipping_amount","shipping_method","tax_amount","discount_amount","currency","order_id","description","customer_id","customer_type","return_url","cancel_url","callback_url","products","orders","address","checkout_token","payment_type"],"address_data":{"name":"Fae Koelpin","email":"<EMAIL>","phone":"+13198941196","country":"PE","state":"Utah","city":"Bashirianfurt","address":"2358 Jadon Stream","zip_code":"09306-7143"}} 
[2025-08-15 16:53:20] local.ERROR: Botble\HyperPay\Services\HyperPayApiService::checkout(): Argument #2 ($user) must be of type ?Illuminate\Database\Eloquent\Model, class@anonymous given, called in D:\laragon\www\martfury\platform\plugins\hyperpay\src\Services\Abstracts\HyperPayPaymentAbstract.php on line 104 {"userId":1,"exception":"[object] (TypeError(code: 0): Botble\\HyperPay\\Services\\HyperPayApiService::checkout(): Argument #2 ($user) must be of type ?Illuminate\\Database\\Eloquent\\Model, class@anonymous given, called in D:\\laragon\\www\\martfury\\platform\\plugins\\hyperpay\\src\\Services\\Abstracts\\HyperPayPaymentAbstract.php on line 104 at D:\\laragon\\www\\martfury\\platform\\plugins\\hyperpay\\src\\Services\\HyperPayApiService.php:144)
[stacktrace]
#0 D:\\laragon\\www\\martfury\\platform\\plugins\\hyperpay\\src\\Services\\Abstracts\\HyperPayPaymentAbstract.php(104): Botble\\HyperPay\\Services\\HyperPayApiService->checkout(Array, Object(class@anonymous), 528.0, 'visa', Object(Illuminate\\Http\\Request))
#1 D:\\laragon\\www\\martfury\\platform\\plugins\\hyperpay\\src\\Services\\Gateways\\HyperPayPaymentService.php(56): Botble\\HyperPay\\Services\\Abstracts\\HyperPayPaymentAbstract->createCheckout(Array)
#2 D:\\laragon\\www\\martfury\\platform\\plugins\\hyperpay\\src\\Services\\Abstracts\\HyperPayPaymentAbstract.php(33): Botble\\HyperPay\\Services\\Gateways\\HyperPayPaymentService->makePayment(Array)
#3 D:\\laragon\\www\\martfury\\platform\\plugins\\hyperpay\\src\\Providers\\HookServiceProvider.php(166): Botble\\HyperPay\\Services\\Abstracts\\HyperPayPaymentAbstract->execute(Array)
#4 [internal function]: Botble\\HyperPay\\Providers\\HookServiceProvider->checkoutWithHyperPay(Array, Object(Botble\\Ecommerce\\Http\\Requests\\CheckoutRequest))
#5 D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Supports\\Filter.php(25): call_user_func_array(Array, Array)
#6 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Botble\\Base\\Supports\\Filter->fire('payment-after-p...', Array)
#7 D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php(39): Illuminate\\Support\\Facades\\Facade::__callStatic('fire', Array)
#8 D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php(789): apply_filters('payment-after-p...', Array, Object(Botble\\Ecommerce\\Http\\Requests\\CheckoutRequest))
#9 [internal function]: Botble\\Ecommerce\\Providers\\HookServiceProvider->Botble\\Ecommerce\\Providers\\{closure}(Array, Object(Botble\\Ecommerce\\Http\\Requests\\CheckoutRequest))
#10 D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Supports\\Filter.php(25): call_user_func_array(Object(Closure), Array)
#11 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Botble\\Base\\Supports\\Filter->fire('filter_ecommerc...', Array)
#12 D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php(39): Illuminate\\Support\\Facades\\Facade::__callStatic('fire', Array)
#13 D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php(622): apply_filters('filter_ecommerc...', Array, Object(Botble\\Ecommerce\\Http\\Requests\\CheckoutRequest))
#14 D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php(235): Botble\\Marketplace\\Providers\\OrderSupportServiceProvider->processPaymentMethodPostCheckout(Object(Botble\\Ecommerce\\Http\\Requests\\CheckoutRequest), 528.0)
#15 [internal function]: Botble\\Marketplace\\Providers\\OrderSupportServiceProvider->processPostCheckoutOrder(Object(Illuminate\\Database\\Eloquent\\Collection), Object(Botble\\Ecommerce\\Http\\Requests\\CheckoutRequest), '2bbdbd08f9ec9fc...', Array, Object(Botble\\Base\\Http\\Responses\\BaseHttpResponse))
#16 D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Supports\\Filter.php(25): call_user_func_array(Array, Array)
#17 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Botble\\Base\\Supports\\Filter->fire('post_checkout_o...', Array)
#18 D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php(39): Illuminate\\Support\\Facades\\Facade::__callStatic('fire', Array)
#19 D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Fronts\\PublicCheckoutController.php(690): apply_filters('post_checkout_o...', Object(Illuminate\\Database\\Eloquent\\Collection), Object(Botble\\Ecommerce\\Http\\Requests\\CheckoutRequest), '2bbdbd08f9ec9fc...', Array, Object(Botble\\Base\\Http\\Responses\\BaseHttpResponse))
#20 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): Botble\\Ecommerce\\Http\\Controllers\\Fronts\\PublicCheckoutController->postCheckout('2bbdbd08f9ec9fc...', Object(Botble\\Ecommerce\\Http\\Requests\\CheckoutRequest), Object(Botble\\Ecommerce\\Services\\HandleShippingFeeService), Object(Botble\\Ecommerce\\Services\\HandleApplyCouponService), Object(Botble\\Ecommerce\\Services\\HandleRemoveCouponService), Object(Botble\\Ecommerce\\Services\\HandleApplyPromotionsService))
#21 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('postCheckout', Array)
#22 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Botble\\Ecommerce\\Http\\Controllers\\Fronts\\PublicCheckoutController), 'postCheckout')
#23 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#24 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#25 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\laragon\\www\\martfury\\platform\\plugins\\language\\src\\Http\\Middleware\\LocalizationRedirectFilter.php(53): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Botble\\Language\\Http\\Middleware\\LocalizationRedirectFilter->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 D:\\laragon\\www\\martfury\\platform\\plugins\\language\\src\\Http\\Middleware\\LocaleSessionRedirect.php(81): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Botble\\Language\\Http\\Middleware\\LocaleSessionRedirect->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Http\\Middleware\\CoreMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Botble\\Base\\Http\\Middleware\\CoreMiddleware->Botble\\Base\\Http\\Middleware\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Http\\Middleware\\EnsureLicenseHasBeenActivated.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Botble\\Base\\Http\\Middleware\\EnsureLicenseHasBeenActivated->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Http\\Middleware\\CoreMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#36 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Botble\\Base\\Http\\Middleware\\CoreMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Middleware\\TrackAbandonedCart.php(18): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Botble\\Ecommerce\\Http\\Middleware\\TrackAbandonedCart->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Middleware\\CaptureCouponMiddleware.php(14): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Botble\\Ecommerce\\Http\\Middleware\\CaptureCouponMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Middleware\\CaptureFootprintsMiddleware.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Botble\\Ecommerce\\Http\\Middleware\\CaptureFootprintsMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Http\\Middleware\\HttpsProtocolMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Botble\\Base\\Http\\Middleware\\HttpsProtocolMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Http\\Middleware\\AdminLocaleMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Botble\\Base\\Http\\Middleware\\AdminLocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Http\\Middleware\\LocaleMiddleware.php(29): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Botble\\Base\\Http\\Middleware\\LocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 D:\\laragon\\www\\martfury\\vendor\\botble\\installer\\src\\Http\\Middleware\\RedirectIfNotInstalledMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Botble\\Installer\\Http\\Middleware\\RedirectIfNotInstalledMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 D:\\laragon\\www\\martfury\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#61 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#68 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#69 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#70 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#71 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#72 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#73 D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\js-validation\\src\\RemoteValidationMiddleware.php(43): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#74 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Botble\\JsValidation\\RemoteValidationMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#75 D:\\laragon\\www\\martfury\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#76 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#77 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#78 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#79 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#80 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#81 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#82 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#83 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#84 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#85 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#86 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#87 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#88 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#89 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#90 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#91 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#92 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#93 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#94 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#95 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#96 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#97 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#98 D:\\laragon\\www\\martfury\\public\\index.php(23): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#99 {main}
"} 
[2025-08-15 16:58:56] local.INFO: HyperPay API Service Config Loaded {"gateway_url":"https://eu-test.oppwa.com","currency":"SAR","has_access_token":true,"has_entity_id":true,"sandbox_mode":"1"} 
[2025-08-15 16:58:56] local.INFO: HyperPay Hook Service Provider - Payment Data {"payment_data":{"amount":528.0,"payment_fee":0.0,"shipping_amount":0.0,"shipping_method":"Default","tax_amount":48.0,"discount_amount":0.0,"currency":"SAR","order_id":[53],"description":"Pay for your order #53 at martfury.gc","customer_id":2,"customer_type":"Botble\\Ecommerce\\Models\\Customer","return_url":"https://martfury.gc/checkout/2bbdbd08f9ec9fc36d8b3e7ced0cf369/success","cancel_url":"https://martfury.gc/checkout/2bbdbd08f9ec9fc36d8b3e7ced0cf369?error=1&error_type=payment","callback_url":"https://martfury.gc/checkout/2bbdbd08f9ec9fc36d8b3e7ced0cf369/success","products":[{"id":38,"name":"Nikon HD camera","image":"https://martfury.gc/storage/products/6.jpg","price":480.0,"price_per_order":528.0,"qty":1}],"orders":{"Illuminate\\Database\\Eloquent\\Collection":[{"id":53,"code":"#10000052","user_id":2,"shipping_option":"3","shipping_method":{"value":"default","label":"Default"},"status":{"value":"pending","label":"Pending"},"amount":"528.00","tax_amount":"48.00","shipping_amount":"0.00","payment_fee":"0.00","description":null,"coupon_code":null,"discount_amount":"0.00","sub_total":"480.00","is_confirmed":0,"discount_description":null,"is_finished":1,"cancellation_reason":null,"cancellation_reason_description":null,"completed_at":null,"token":"2bbdbd08f9ec9fc36d8b3e7ced0cf369","payment_id":93,"created_at":"2025-08-15T16:37:22.000000Z","updated_at":"2025-08-15T16:58:56.000000Z","proof_file":null,"store_id":3,"private_notes":null,"address":{"id":47,"name":"Fae Koelpin","phone":"+13198941196","email":"<EMAIL>","country":"PE","state":"Utah","city":"Bashirianfurt","address":"2358 Jadon Stream","order_id":53,"zip_code":"09306-7143","type":{"value":"shipping_address","label":"Shipping address"}},"products":[{"id":56,"order_id":53,"qty":1,"price":"480.00","tax_amount":"48.00","options":{"image":"products/6.jpg","attributes":"(Color: Black, Size: XXL)","taxRate":10,"taxClasses":{"VAT":10},"options":[],"extras":[],"sku":"SW-125-A0","weight":886},"product_options":[],"product_id":38,"product_name":"Nikon HD camera","product_image":"products/6.jpg","weight":886.0,"restock_quantity":0,"created_at":"2025-08-15T16:37:22.000000Z","updated_at":"2025-08-15T16:58:56.000000Z","product_type":"physical","times_downloaded":0,"license_code":null,"downloaded_at":null,"product":{"id":38,"name":"Nikon HD camera","description":null,"content":null,"status":{"value":"published","label":"Published"},"images":["products/6.jpg"],"video_media":null,"sku":"SW-125-A0","order":0,"quantity":17,"allow_checkout_when_out_of_stock":false,"with_storehouse_management":true,"is_featured":false,"brand_id":3,"is_variation":true,"sale_type":0,"price":480.0,"sale_price":null,"start_date":null,"end_date":null,"length":12.0,"wide":11.0,"height":17.0,"weight":886.0,"tax_id":null,"views":0,"created_at":"2024-10-13T22:15:53.000000Z","updated_at":"2025-08-15T16:38:06.000000Z","stock_status":{"value":"in_stock","label":"In stock"},"created_by_id":0,"created_by_type":"Botble\\ACL\\Models\\User","image":"products/6.jpg","product_type":{"value":"physical","label":"Physical"},"barcode":null,"cost_per_item":null,"generate_license_code":false,"license_code_type":"auto_generate","minimum_order_quantity":0,"maximum_order_quantity":0,"notify_attachment_updated":false,"specification_table_id":null,"store_id":null,"approved_by":0,"original_price":480.0,"front_sale_price":480.0,"variation_info":{"id":15,"product_id":38,"configurable_product_id":6,"is_default":1,"configurable_product":{"id":6,"name":"Nikon HD camera","description":"<ul><li> Unrestrained and portable active stereo speaker</li>

            <li> Free from the confines of wires and chords</li>

            <li> 20 hours of portable capabilities</li>

            <li> Double-ended Coil Cord with 3.5mm Stereo Plugs Included</li>

            <li> 3/4″ Dome Tweeters: 2X and 4″ Woofer: 1X</li></ul>","content":"<p>Short Hooded Coat features a straight body, large pockets with button flaps, ventilation air holes, and a string detail along the hemline. The style is completed with a drawstring hood, featuring Rains’ signature built-in cap. Made from waterproof, matte PU, this lightweight unisex rain jacket is an ode to nostalgia through its classic silhouette and utilitarian design details.</p>

                                <p>- Casual unisex fit</p>



                                <p>- 64% polyester, 36% polyurethane</p>



                                <p>- Water column pressure: 4000 mm</p>



                                <p>- Model is 187cm tall and wearing a size S / M</p>



                                <p>- Unisex fit</p>



                                <p>- Drawstring hood with built-in cap</p>



                                <p>- Front placket with snap buttons</p>



                                <p>- Ventilation under armpit</p>



                                <p>- Adjustable cuffs</p>



                                <p>- Double welted front pockets</p>



                                <p>- Adjustable elastic string at hempen</p>



                                <p>- Ultrasonically welded seams</p>



                                <p>This is a unisex item, please check our clothing & footwear sizing guide for specific Rains jacket sizing information. RAINS comes from the rainy nation of Denmark at the edge of the European continent, close to the ocean and with prevailing westerly winds; all factors that contribute to an average of 121 rain days each year. Arising from these rainy weather conditions comes the attitude that a quick rain shower may be beautiful, as well as moody- but first and foremost requires the right outfit. Rains focus on the whole experience of going outside on rainy days, issuing an invitation to explore even in the most mercurial weather.</p>","status":{"value":"published","label":"Published"},"images":["products/6.jpg"],"video_media":null,"sku":"SW-125-A0","order":0,"quantity":17,"allow_checkout_when_out_of_stock":false,"with_storehouse_management":true,"is_featured":true,"brand_id":3,"is_variation":false,"sale_type":0,"price":480.0,"sale_price":null,"start_date":null,"end_date":null,"length":12.0,"wide":11.0,"height":17.0,"weight":886.0,"tax_id":null,"views":157528,"created_at":"2024-10-13T22:15:52.000000Z","updated_at":"2025-08-15T16:38:06.000000Z","stock_status":{"value":"in_stock","label":"In stock"},"created_by_id":0,"created_by_type":"Botble\\ACL\\Models\\User","image":"products/6.jpg","product_type":{"value":"physical","label":"Physical"},"barcode":null,"cost_per_item":null,"generate_license_code":false,"license_code_type":"auto_generate","minimum_order_quantity":0,"maximum_order_quantity":0,"notify_attachment_updated":false,"specification_table_id":2,"store_id":3,"approved_by":0,"original_price":480.0,"front_sale_price":480.0}}}}]}]},"address":{"name":"Fae Koelpin","email":"<EMAIL>","phone":"+13198941196","country":"PE","state":"Utah","city":"Bashirianfurt","address":"2358 Jadon Stream","zip_code":"09306-7143"},"checkout_token":"2bbdbd08f9ec9fc36d8b3e7ced0cf369"},"order_id_type":"array","order_id_value":[53]} 
[2025-08-15 16:58:56] local.INFO: HyperPay makePayment called with data: {"amount":528.0,"currency":"SAR","order_id":[53],"payment_type":"visa","data_keys":["amount","payment_fee","shipping_amount","shipping_method","tax_amount","discount_amount","currency","order_id","description","customer_id","customer_type","return_url","cancel_url","callback_url","products","orders","address","checkout_token","payment_type"],"address_data":{"name":"Fae Koelpin","email":"<EMAIL>","phone":"+13198941196","country":"PE","state":"Utah","city":"Bashirianfurt","address":"2358 Jadon Stream","zip_code":"09306-7143"}} 
[2025-08-15 16:58:56] local.INFO: HyperPay HTTP Parameters Generated {"amount":528.0,"currency":"SAR","entity_id":"8ac7a4c79483092601948366b9d1011b","merchant_transaction_id":"Rw7ZEYN5e6t3BDnMQoXa1s9iQcYNIHY5naM8Pnm1WPrcsoHDh52t82aoTP2Tdn8J","register_user":false,"has_billing":false,"parameter_count":9} 
[2025-08-15 16:58:56] local.INFO: HyperPay HTTP Client POST Request {"url":"https://eu-test.oppwa.com/v1/checkouts","parameters":{"entityId":"8ac7a4c79483092601948366b9d1011b","amount":"528.00","currency":"SAR","paymentType":"DB","merchantTransactionId":"Rw7ZEYN5e6t3BDnMQoXa1s9iQcYNIHY5naM8Pnm1WPrcsoHDh52t82aoTP2Tdn8J","notificationUrl":"https://martfury.gc/hyperpay/webhook","customer.email":"<EMAIL>","customer.givenName":"Fae","customer.surname":"Koelpin"},"access_token_present":true} 
[2025-08-15 16:58:57] local.INFO: HyperPay HTTP Client POST Response {"status_code":200,"response_body":"{\"result\":{\"code\":\"000.200.100\",\"description\":\"successfully created checkout\"},\"buildNumber\":\"8aa3728a6ba24ed3c4a6c6e9064ffd726f1bee67@2025-08-11 05:22:09 +0000\",\"timestamp\":\"2025-08-15 16:58:58+0000\",\"ndc\":\"7AFC5E0AD4DB9601356CE6D30B79F7A8.uat01-vm-tx01\",\"id\":\"7AFC5E0AD4DB9601356CE6D30B79F7A8.uat01-vm-tx01\"}"} 
[2025-08-15 16:58:57] local.ERROR: Botble\HyperPay\Services\Support\HttpResponse::setUser(): Argument #1 ($user) must be of type Illuminate\Database\Eloquent\Model, null given, called in D:\laragon\www\martfury\platform\plugins\hyperpay\src\Services\HyperPayApiService.php on line 207 {"userId":1,"exception":"[object] (TypeError(code: 0): Botble\\HyperPay\\Services\\Support\\HttpResponse::setUser(): Argument #1 ($user) must be of type Illuminate\\Database\\Eloquent\\Model, null given, called in D:\\laragon\\www\\martfury\\platform\\plugins\\hyperpay\\src\\Services\\HyperPayApiService.php on line 207 at D:\\laragon\\www\\martfury\\platform\\plugins\\hyperpay\\src\\Services\\Support\\HttpResponse.php:67)
[stacktrace]
#0 D:\\laragon\\www\\martfury\\platform\\plugins\\hyperpay\\src\\Services\\HyperPayApiService.php(207): Botble\\HyperPay\\Services\\Support\\HttpResponse->setUser(NULL)
#1 D:\\laragon\\www\\martfury\\platform\\plugins\\hyperpay\\src\\Services\\HyperPayApiService.php(161): Botble\\HyperPay\\Services\\HyperPayApiService->prepareCheckout(NULL, Array, Object(Illuminate\\Http\\Request))
#2 D:\\laragon\\www\\martfury\\platform\\plugins\\hyperpay\\src\\Services\\Abstracts\\HyperPayPaymentAbstract.php(119): Botble\\HyperPay\\Services\\HyperPayApiService->checkout(Array, NULL, 528.0, 'visa', Object(Illuminate\\Http\\Request))
#3 D:\\laragon\\www\\martfury\\platform\\plugins\\hyperpay\\src\\Services\\Gateways\\HyperPayPaymentService.php(56): Botble\\HyperPay\\Services\\Abstracts\\HyperPayPaymentAbstract->createCheckout(Array)
#4 D:\\laragon\\www\\martfury\\platform\\plugins\\hyperpay\\src\\Services\\Abstracts\\HyperPayPaymentAbstract.php(33): Botble\\HyperPay\\Services\\Gateways\\HyperPayPaymentService->makePayment(Array)
#5 D:\\laragon\\www\\martfury\\platform\\plugins\\hyperpay\\src\\Providers\\HookServiceProvider.php(166): Botble\\HyperPay\\Services\\Abstracts\\HyperPayPaymentAbstract->execute(Array)
#6 [internal function]: Botble\\HyperPay\\Providers\\HookServiceProvider->checkoutWithHyperPay(Array, Object(Botble\\Ecommerce\\Http\\Requests\\CheckoutRequest))
#7 D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Supports\\Filter.php(25): call_user_func_array(Array, Array)
#8 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Botble\\Base\\Supports\\Filter->fire('payment-after-p...', Array)
#9 D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php(39): Illuminate\\Support\\Facades\\Facade::__callStatic('fire', Array)
#10 D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php(789): apply_filters('payment-after-p...', Array, Object(Botble\\Ecommerce\\Http\\Requests\\CheckoutRequest))
#11 [internal function]: Botble\\Ecommerce\\Providers\\HookServiceProvider->Botble\\Ecommerce\\Providers\\{closure}(Array, Object(Botble\\Ecommerce\\Http\\Requests\\CheckoutRequest))
#12 D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Supports\\Filter.php(25): call_user_func_array(Object(Closure), Array)
#13 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Botble\\Base\\Supports\\Filter->fire('filter_ecommerc...', Array)
#14 D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php(39): Illuminate\\Support\\Facades\\Facade::__callStatic('fire', Array)
#15 D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php(622): apply_filters('filter_ecommerc...', Array, Object(Botble\\Ecommerce\\Http\\Requests\\CheckoutRequest))
#16 D:\\laragon\\www\\martfury\\platform\\plugins\\marketplace\\src\\Providers\\OrderSupportServiceProvider.php(235): Botble\\Marketplace\\Providers\\OrderSupportServiceProvider->processPaymentMethodPostCheckout(Object(Botble\\Ecommerce\\Http\\Requests\\CheckoutRequest), 528.0)
#17 [internal function]: Botble\\Marketplace\\Providers\\OrderSupportServiceProvider->processPostCheckoutOrder(Object(Illuminate\\Database\\Eloquent\\Collection), Object(Botble\\Ecommerce\\Http\\Requests\\CheckoutRequest), '2bbdbd08f9ec9fc...', Array, Object(Botble\\Base\\Http\\Responses\\BaseHttpResponse))
#18 D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Supports\\Filter.php(25): call_user_func_array(Array, Array)
#19 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Botble\\Base\\Supports\\Filter->fire('post_checkout_o...', Array)
#20 D:\\laragon\\www\\martfury\\platform\\core\\base\\helpers\\action-filter.php(39): Illuminate\\Support\\Facades\\Facade::__callStatic('fire', Array)
#21 D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Fronts\\PublicCheckoutController.php(690): apply_filters('post_checkout_o...', Object(Illuminate\\Database\\Eloquent\\Collection), Object(Botble\\Ecommerce\\Http\\Requests\\CheckoutRequest), '2bbdbd08f9ec9fc...', Array, Object(Botble\\Base\\Http\\Responses\\BaseHttpResponse))
#22 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): Botble\\Ecommerce\\Http\\Controllers\\Fronts\\PublicCheckoutController->postCheckout('2bbdbd08f9ec9fc...', Object(Botble\\Ecommerce\\Http\\Requests\\CheckoutRequest), Object(Botble\\Ecommerce\\Services\\HandleShippingFeeService), Object(Botble\\Ecommerce\\Services\\HandleApplyCouponService), Object(Botble\\Ecommerce\\Services\\HandleRemoveCouponService), Object(Botble\\Ecommerce\\Services\\HandleApplyPromotionsService))
#23 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('postCheckout', Array)
#24 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Botble\\Ecommerce\\Http\\Controllers\\Fronts\\PublicCheckoutController), 'postCheckout')
#25 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#26 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#27 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\laragon\\www\\martfury\\platform\\plugins\\language\\src\\Http\\Middleware\\LocalizationRedirectFilter.php(53): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Botble\\Language\\Http\\Middleware\\LocalizationRedirectFilter->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 D:\\laragon\\www\\martfury\\platform\\plugins\\language\\src\\Http\\Middleware\\LocaleSessionRedirect.php(81): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Botble\\Language\\Http\\Middleware\\LocaleSessionRedirect->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Http\\Middleware\\CoreMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Botble\\Base\\Http\\Middleware\\CoreMiddleware->Botble\\Base\\Http\\Middleware\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Http\\Middleware\\EnsureLicenseHasBeenActivated.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Botble\\Base\\Http\\Middleware\\EnsureLicenseHasBeenActivated->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Http\\Middleware\\CoreMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#38 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Botble\\Base\\Http\\Middleware\\CoreMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Middleware\\TrackAbandonedCart.php(18): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Botble\\Ecommerce\\Http\\Middleware\\TrackAbandonedCart->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Middleware\\CaptureCouponMiddleware.php(14): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Botble\\Ecommerce\\Http\\Middleware\\CaptureCouponMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\laragon\\www\\martfury\\platform\\plugins\\ecommerce\\src\\Http\\Middleware\\CaptureFootprintsMiddleware.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Botble\\Ecommerce\\Http\\Middleware\\CaptureFootprintsMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Http\\Middleware\\HttpsProtocolMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Botble\\Base\\Http\\Middleware\\HttpsProtocolMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Http\\Middleware\\AdminLocaleMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Botble\\Base\\Http\\Middleware\\AdminLocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\base\\src\\Http\\Middleware\\LocaleMiddleware.php(29): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Botble\\Base\\Http\\Middleware\\LocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 D:\\laragon\\www\\martfury\\vendor\\botble\\installer\\src\\Http\\Middleware\\RedirectIfNotInstalledMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Botble\\Installer\\Http\\Middleware\\RedirectIfNotInstalledMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 D:\\laragon\\www\\martfury\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#63 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#69 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#70 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#71 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#72 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#73 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#74 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#75 D:\\laragon\\www\\martfury\\vendor\\botble\\platform\\js-validation\\src\\RemoteValidationMiddleware.php(43): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#76 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Botble\\JsValidation\\RemoteValidationMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#77 D:\\laragon\\www\\martfury\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#78 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#79 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#80 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#81 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#82 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#83 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#84 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#85 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#86 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#87 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#88 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#89 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#90 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#91 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#92 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#93 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#94 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#95 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#96 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#97 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#98 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#99 D:\\laragon\\www\\martfury\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#100 D:\\laragon\\www\\martfury\\public\\index.php(23): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#101 {main}
"} 
